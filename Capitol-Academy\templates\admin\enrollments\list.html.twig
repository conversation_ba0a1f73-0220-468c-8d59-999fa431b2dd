{% extends 'admin/base.html.twig' %}

{% block title %}Enrollments Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Enrollments Management{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item active">Enrollments</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Enrollments Management',
    'page_icon': 'fas fa-user-graduate',
    'search_placeholder': 'Search enrollments...',
    'create_button': {
        'url': path('admin_enrollment_create'),
        'text': 'Manual Enrollment',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Enrollments',
            'value': enrollments|length,
            'icon': 'fas fa-users',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': enrollments|filter(enrollment => enrollment.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Completed',
            'value': enrollments|filter(enrollment => enrollment.isCompleted)|length,
            'icon': 'fas fa-graduation-cap',
            'color': '#17a2b8',
            'gradient': 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': enrollments|filter(enrollment => enrollment.enrolledAt and enrollment.enrolledAt > date('-30 days'))|length,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Student'},
            {'text': 'Course'},
            {'text': 'Enrolled Date'},
            {'text': 'Status'},
            {'text': 'Progress'},
            {'text': 'Payment'},
            {'text': 'Actions', 'style': 'width: 150px;'}
        ] %}

        {% set table_rows = [] %}
        {% for enrollment in enrollments %}
            {% set row_cells = [
                {
                    'content': '<div class="d-flex align-items-center">
                        <div class="enrollment-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mr-2" style="width: 40px; height: 40px; font-size: 14px; font-weight: 600;">
                            ' ~ enrollment.user.firstName|first|upper ~ enrollment.user.lastName|first|upper ~ '
                        </div>
                        <div>
                            <div>
                                <a href="' ~ path('admin_user_details', {'emailPrefix': enrollment.user.emailPrefix}) ~ '" class="enrollment-student-name font-weight-bold text-primary text-decoration-none" style="color: #011a2d !important;">
                                    ' ~ enrollment.user.firstName ~ ' ' ~ enrollment.user.lastName ~ '
                                </a>
                            </div>
                        </div>
                    </div>'
                },
                {
                    'content': '<div>
                        <div>
                            <a href="' ~ path('admin_course_preview', {'code': enrollment.course.code}) ~ '" class="text-decoration-none" style="color: #011a2d;">
                                <code class="enrollment-course-code bg-light text-dark" style="padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 600; margin-right: 0.5rem;">' ~ enrollment.course.code ~ '</code>
                            </a>
                        </div>
                    </div>'
                },
                {
                    'content': '<div>
                        <span class="text-dark font-weight-medium">' ~ enrollment.enrolledAt|date('M j, Y') ~ '</span>
                    </div>'
                },
                {
                    'content': (enrollment.isCompleted ?
                        '<span class="badge" style="background: #28a745; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-check-circle mr-1"></i> Completed</span>
                        <br>' :
                        (enrollment.isActive ?
                            '<span class="badge" style="background: #007bff; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-play-circle mr-1"></i> Active</span>' :
                            '<span class="badge" style="background: #6c757d; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-pause-circle mr-1"></i> Inactive</span>'
                        )
                    )
                },
                {
                    'content': '<div class="progress" style="height: 20px; background:rgb(179, 202, 225); border-radius: 10px;">
                        <div class="progress-bar" role="progressbar"
                             style="width: ' ~ enrollment.progressPercentage ~ '%; background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); border-radius: 10px;"
                             aria-valuenow="' ~ enrollment.progressPercentage ~ '"
                             aria-valuemin="0" aria-valuemax="100">
                        </div>
                    </div>'
                },
                {
                    'content': (enrollment.payment ?
                        (enrollment.payment.isSuccessful ?
                            '<span class="badge" style="background: #28a745; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-credit-card mr-1"></i> Paid</span>
                            <br>' :
                            '<span class="badge" style="background: #ffc107; color: #212529; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-clock mr-1"></i> ' ~ enrollment.payment.status|title ~ '</span>'
                        ) :
                        '<span class="badge" style="background: #17a2b8; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-gift mr-1"></i> Free</span>'
                    )
                },
                {
                    'content': '<div class="btn-group" role="group">
                        <a href="' ~ path('admin_enrollment_details_by_code', {'courseCode': enrollment.course.code, 'studentName': enrollment.user.fullName|replace({' ': '-'})|lower}) ~ '" class="btn btn-sm shadow-sm" style="background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="View Details"><i class="fas fa-eye"></i></a>
                        ' ~ (enrollment.isCertified ?
                            '<span class="btn btn-sm shadow-sm" style="background: #6c757d; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px; cursor: not-allowed; opacity: 0.6;" title="Certified enrollments cannot be edited"><i class="fas fa-edit"></i></span>' :
                            '<a href="' ~ path('admin_enrollment_edit', {'courseCode': enrollment.course.code, 'studentName': enrollment.user.fullName|replace({' ': '-'})|lower}) ~ '" class="btn btn-sm shadow-sm" style="background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Edit Enrollment"><i class="fas fa-edit"></i></a>'
                        ) ~ '
                        ' ~ (enrollment.isCertified ? '' :
                            (enrollment.status != 'blocked' ?
                                '<button type="button" class="btn btn-sm shadow-sm" style="background: #ffc107; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Block Enrollment" onclick="showBlockModal(' ~ enrollment.id ~ ', \'' ~ enrollment.user.firstName ~ ' ' ~ enrollment.user.lastName ~ '\', \'' ~ enrollment.course.code ~ '\')"><i class="fas fa-ban"></i></button>' :
                                '<button type="button" class="btn btn-sm shadow-sm" style="background: #28a745; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Unblock Enrollment" onclick="showUnblockModal(' ~ enrollment.id ~ ', \'' ~ enrollment.user.firstName ~ ' ' ~ enrollment.user.lastName ~ '\', \'' ~ enrollment.course.code ~ '\')"><i class="fas fa-check"></i></button>'
                            )
                        ) ~ '
                        ' ~ (enrollment.isCertified ?
                            '<span class="btn btn-sm shadow-sm" style="background: #6c757d; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; cursor: not-allowed; opacity: 0.6;" title="Certified enrollments cannot be deleted"><i class="fas fa-trash"></i></span>' :
                            '<button type="button" class="btn btn-sm shadow-sm" style="background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;" title="Delete Enrollment" onclick="showDeleteModal(' ~ enrollment.id ~ ', \'' ~ enrollment.user.firstName ~ ' ' ~ enrollment.user.lastName ~ '\', \'' ~ enrollment.course.code ~ '\')"><i class="fas fa-trash"></i></button>'
                        ) ~ '
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'enrollment-row',
            'empty_message': 'No enrollments found',
            'empty_icon': 'fas fa-user-graduate',
            'empty_description': 'Try adjusting your search or create a new enrollment.',
            'search_config': {
                'fields': ['.enrollment-student-name', '.enrollment-course-code', '.enrollment-course-title']
            }
        } %}
    {% endblock %}
{% endembed %}

<!-- Enrollment Action Modals -->
<!-- Block Enrollment Modal -->
<div class="modal fade" id="blockEnrollmentModal" tabindex="-1" aria-labelledby="blockEnrollmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content" style="border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);">
            <div class="modal-header" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: #212529; border: none; padding: 1rem;">
                <h6 class="modal-title" id="blockEnrollmentModalLabel" style="font-weight: 600;">
                    <i class="fas fa-ban me-2"></i>Block Enrollment
                </h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="padding: 1rem; text-align: center;">
                <p class="mb-3" style="color: #011a2d;">Are you sure you want to block this enrollment?</p>
                <div id="blockEnrollmentDetails" class="text-muted mb-3"></div>
                <small class="text-muted">This will prevent the student from accessing the course content.</small>
            </div>
            <div class="modal-footer" style="border: none; padding: 1rem; background: #f8f9fa;">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-sm" id="confirmBlockBtn" style="background: #ffc107; color: #212529; border: none;">
                    <i class="fas fa-ban me-1"></i>Block Enrollment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Unblock Enrollment Modal -->
<div class="modal fade" id="unblockEnrollmentModal" tabindex="-1" aria-labelledby="unblockEnrollmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content" style="border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);">
            <div class="modal-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 1rem;">
                <h6 class="modal-title" id="unblockEnrollmentModalLabel" style="font-weight: 600;">
                    <i class="fas fa-check me-2"></i>Unblock Enrollment
                </h6>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="padding: 1rem; text-align: center;">
                <p class="mb-3" style="color: #011a2d;">Are you sure you want to unblock this enrollment?</p>
                <div id="unblockEnrollmentDetails" class="text-muted mb-3"></div>
                <small class="text-muted">This will restore the student's access to the course content.</small>
            </div>
            <div class="modal-footer" style="border: none; padding: 1rem; background: #f8f9fa;">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-sm" id="confirmUnblockBtn" style="background: #28a745; color: white; border: none;">
                    <i class="fas fa-check me-1"></i>Unblock Enrollment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Enrollment Modal -->
<div class="modal fade" id="deleteEnrollmentModal" tabindex="-1" aria-labelledby="deleteEnrollmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content" style="border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);">
            <div class="modal-header" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;">
                <h6 class="modal-title" id="deleteEnrollmentModalLabel" style="font-weight: 600;">
                    <i class="fas fa-trash me-2"></i>Delete Enrollment
                </h6>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="padding: 1rem; text-align: center;">
                <p class="mb-3" style="color: #011a2d;">Are you sure you want to delete this enrollment?</p>
                <div id="deleteEnrollmentDetails" class="text-muted mb-3"></div>
                <small class="text-danger"><strong>Warning:</strong> This action cannot be undone.</small>
            </div>
            <div class="modal-footer" style="border: none; padding: 1rem; background: #f8f9fa;">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-sm" id="confirmDeleteBtn" style="background: #a90418; color: white; border: none;">
                    <i class="fas fa-trash me-1"></i>Delete Enrollment
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.enrollment-row',
        ['.enrollment-student-name', '.enrollment-course-code', '.enrollment-course-title']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Modal functions for enrollment actions
function showBlockModal(enrollmentId, studentName, courseCode) {
    document.getElementById('blockEnrollmentDetails').innerHTML = `
        <strong>${studentName}</strong><br>
        <small>Course: ${courseCode}</small>
    `;

    const modal = new bootstrap.Modal(document.getElementById('blockEnrollmentModal'));
    const confirmBtn = document.getElementById('confirmBlockBtn');

    // Remove existing event listeners
    confirmBtn.replaceWith(confirmBtn.cloneNode(true));
    const newConfirmBtn = document.getElementById('confirmBlockBtn');

    newConfirmBtn.addEventListener('click', function() {
        modal.hide();
        blockEnrollment(enrollmentId);
    });

    modal.show();
}

function blockEnrollment(enrollmentId) {
    fetch(`{{ path('admin_enrollment_block', {'id': '0'}) }}`.replace('0', enrollmentId), {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to block enrollment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while blocking the enrollment');
    });
}

function showUnblockModal(enrollmentId, studentName, courseCode) {
    document.getElementById('unblockEnrollmentDetails').innerHTML = `
        <strong>${studentName}</strong><br>
        <small>Course: ${courseCode}</small>
    `;

    const modal = new bootstrap.Modal(document.getElementById('unblockEnrollmentModal'));
    const confirmBtn = document.getElementById('confirmUnblockBtn');

    // Remove existing event listeners
    confirmBtn.replaceWith(confirmBtn.cloneNode(true));
    const newConfirmBtn = document.getElementById('confirmUnblockBtn');

    newConfirmBtn.addEventListener('click', function() {
        modal.hide();
        unblockEnrollment(enrollmentId);
    });

    modal.show();
}

function unblockEnrollment(enrollmentId) {
    fetch(`{{ path('admin_enrollment_unblock', {'id': '0'}) }}`.replace('0', enrollmentId), {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to unblock enrollment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while unblocking the enrollment');
    });
}

function showDeleteModal(enrollmentId, studentName, courseCode) {
    document.getElementById('deleteEnrollmentDetails').innerHTML = `
        <strong>${studentName}</strong><br>
        <small>Course: ${courseCode}</small>
    `;

    const modal = new bootstrap.Modal(document.getElementById('deleteEnrollmentModal'));
    const confirmBtn = document.getElementById('confirmDeleteBtn');

    // Remove existing event listeners
    confirmBtn.replaceWith(confirmBtn.cloneNode(true));
    const newConfirmBtn = document.getElementById('confirmDeleteBtn');

    newConfirmBtn.addEventListener('click', function() {
        modal.hide();
        deleteEnrollment(enrollmentId);
    });

    modal.show();
}

function deleteEnrollment(enrollmentId) {
    fetch(`{{ path('admin_enrollment_delete', {'id': '0'}) }}`.replace('0', enrollmentId), {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to delete enrollment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the enrollment');
    });
}
</script>
{% endblock %}