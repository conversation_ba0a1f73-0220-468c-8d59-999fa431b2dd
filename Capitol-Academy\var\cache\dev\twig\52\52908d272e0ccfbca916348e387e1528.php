<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/courses/edit.html.twig */
class __TwigTemplate_fb9f337deacad498844e5f8dfb04fa11 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/courses/edit.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/courses/edit.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Edit Course - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Edit Course";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses");
        yield "\">Courses</a></li>
<li class=\"breadcrumb-item active\">Edit Course</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Course
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Courses Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" id=\"course-form\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"";
        // line 57
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("course_edit"), "html", null, true);
        yield "\">
            <input type=\"hidden\" name=\"is_active\" value=\"";
        // line 58
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 58, $this->source); })()), "isActive", [], "any", false, false, false, 58)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("1") : ("0"));
        yield "\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Course Code and Title Row -->
                            <div class=\"row\">
                                <!-- Course Code -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"code\" class=\"form-label\">
                                            <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Code <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"code\"
                                               name=\"code\"
                                               value=\"";
        // line 76
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 76, $this->source); })()), "code", [], "any", false, false, false, 76), "html", null, true);
        yield "\"
                                               placeholder=\"e.g., TRAD101, FIN200\"
                                               required
                                               maxlength=\"10\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid course code.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                            Course code should be unique and descriptive (e.g., TRAD101, FIN200)
                                        </small>
                                    </div>
                                </div>

                                <!-- Course Title -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-book\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               value=\"";
        // line 102
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 102, $this->source); })()), "title", [], "any", false, false, false, 102), "html", null, true);
        yield "\"
                                               placeholder=\"Enter course title\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid course title.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                            Enter a descriptive title for your course
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Course Description -->
                            <div class=\"form-group\">
                                <label for=\"description\" class=\"form-label\">
                                    <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Course Description <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"description\"
                                          name=\"description\"
                                          rows=\"8\"
                                          placeholder=\"Enter detailed course description...\"
                                          required
                                          style=\"min-height: 150px; border: 2px solid #ced4da; border-radius: 0.375rem;\">";
        // line 130
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 130, $this->source); })()), "description", [], "any", false, false, false, 130), "html", null, true);
        yield "</textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide a course description.
                                </div>
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                    Provide a comprehensive description of what students will learn
                                </small>
                            </div>

                            <!-- Category and Duration Row -->
                            <div class=\"row\">
                                <!-- Category -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"category\" class=\"form-label\">
                                            <i class=\"fas fa-tags\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Category <span class=\"text-danger\">*</span>
                                        </label>
                                        <select class=\"form-control enhanced-field\"
                                                id=\"category\"
                                                name=\"category_id\"
                                                required
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                            <option value=\"\">Select a category</option>
                                            ";
        // line 155
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 155, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["category"]) {
            // line 156
            yield "                                                <option value=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "id", [], "any", false, false, false, 156), "html", null, true);
            yield "\" ";
            yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 156, $this->source); })()), "category", [], "any", false, false, false, 156) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 156, $this->source); })()), "category", [], "any", false, false, false, 156) == CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 156)))) ? ("selected") : (""));
            yield ">
                                                    ";
            // line 157
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 157), "html", null, true);
            yield "
                                                </option>
                                            ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['category'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 160
        yield "                                        </select>
                                        <div class=\"invalid-feedback\">
                                            Please select a category.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                            Choose the most appropriate category for your course
                                        </small>
                                    </div>
                                </div>

                                <!-- Duration -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"duration\" class=\"form-label\">
                                            <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Duration (Minutes) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"duration\"
                                               name=\"duration\"
                                               value=\"";
        // line 182
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 182, $this->source); })()), "duration", [], "any", false, false, false, 182), "html", null, true);
        yield "\"
                                               placeholder=\"e.g., 120\"
                                               min=\"1\"
                                               max=\"10000\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid duration.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                            Enter the total duration in minutes
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Price -->
                            <div class=\"form-group\">
                                <label for=\"price\" class=\"form-label\">
                                    <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Price (USD) <span class=\"text-danger\">*</span>
                                </label>
                                <input type=\"number\"
                                       class=\"form-control enhanced-field\"
                                       id=\"price\"
                                       name=\"price\"
                                       value=\"";
        // line 209
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 209, $this->source); })()), "price", [], "any", false, false, false, 209), "html", null, true);
        yield "\"
                                       placeholder=\"0.00\"
                                       step=\"0.01\"
                                       min=\"0\"
                                       required
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                <div class=\"invalid-feedback\">
                                    Please provide a valid price.
                                </div>
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                    Set the course price in USD (use 0 for free courses)
                                </small>
                            </div>

                            <!-- Learning Outcomes -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Learning Outcomes <span class=\"text-danger\">*</span>
                                </label>
                                <div id=\"learning-outcomes-container\">
                                    ";
        // line 231
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 231, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 231) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 231, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 231)) > 0))) {
            // line 232
            yield "                                        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 232, $this->source); })()), "learningOutcomes", [], "any", false, false, false, 232));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                // line 233
                yield "                                        <div class=\"input-group mb-2\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"learning_outcomes[]\"
                                                   value=\"";
                // line 237
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                yield "\"
                                                   placeholder=\"e.g., Master advanced chart analysis techniques\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                ";
                // line 242
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "first", [], "any", false, false, false, 242)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 243
                    yield "                                                <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                                ";
                } else {
                    // line 247
                    yield "                                                <button type=\"button\" class=\"btn btn-danger remove-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                    <i class=\"fas fa-minus\"></i>
                                                </button>
                                                <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                                ";
                }
                // line 254
                yield "                                            </div>
                                        </div>
                                        ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 257
            yield "                                    ";
        } else {
            // line 258
            yield "                                    <div class=\"input-group mb-2\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"learning_outcomes[]\"
                                               placeholder=\"e.g., Master advanced chart analysis techniques\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                    ";
        }
        // line 272
        yield "                                </div>
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                    Add specific learning outcomes that students will achieve
                                </small>
                            </div>

                            <!-- Features -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Features <span class=\"text-danger\">*</span>
                                </label>
                                <div id=\"features-container\">
                                    ";
        // line 286
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 286, $this->source); })()), "features", [], "any", false, false, false, 286) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 286, $this->source); })()), "features", [], "any", false, false, false, 286)) > 0))) {
            // line 287
            yield "                                        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 287, $this->source); })()), "features", [], "any", false, false, false, 287));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                // line 288
                yield "                                        <div class=\"input-group mb-2\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"features[]\"
                                                   value=\"";
                // line 292
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                yield "\"
                                                   placeholder=\"e.g., Interactive trading simulator\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                ";
                // line 297
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "first", [], "any", false, false, false, 297)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 298
                    yield "                                                <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                                ";
                } else {
                    // line 302
                    yield "                                                <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                    <i class=\"fas fa-minus\"></i>
                                                </button>
                                                <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                                ";
                }
                // line 309
                yield "                                            </div>
                                        </div>
                                        ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 312
            yield "                                    ";
        } else {
            // line 313
            yield "                                    <div class=\"input-group mb-2\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"features[]\"
                                               placeholder=\"e.g., Interactive trading simulator\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                    ";
        }
        // line 327
        yield "                                </div>
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                    Highlight key features and benefits of your course
                                </small>
                            </div>

                            <!-- Image Uploads Section -->
                            <div class=\"row\">
                                <!-- Thumbnail Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"thumbnail_image\" class=\"form-label\">
                                            <i class=\"fas fa-image\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Thumbnail Image <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"file\"
                                               class=\"form-control enhanced-file-field\"
                                               id=\"thumbnail_image\"
                                               name=\"thumbnail_image\"
                                               accept=\"image/jpeg,image/png,image/jpg\"
                                               ";
        // line 348
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 348, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 348)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("") : ("required"));
        yield "
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">

                                        ";
        // line 351
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 351, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 351)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 352
            yield "                                        <div class=\"image-preview mt-3 text-center\" id=\"thumbnail-preview\">
                                            <div class=\"professional-thumbnail-container mx-auto\" style=\"width: 100%; max-width: 300px; height: 200px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                                <img src=\"";
            // line 354
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/courses/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 354, $this->source); })()), "thumbnailImage", [], "any", false, false, false, 354))), "html", null, true);
            yield "\" alt=\"Current Thumbnail\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                            </div>
                                            <small class=\"text-muted mt-2 d-block\">Current thumbnail</small>
                                        </div>
                                        ";
        } else {
            // line 359
            yield "                                        <div class=\"image-preview mt-3 text-center\" id=\"thumbnail-preview\" style=\"display: none;\">
                                            <div class=\"professional-thumbnail-container mx-auto\" style=\"width: 100%; max-width: 300px; height: 200px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                                <img src=\"\" alt=\"Thumbnail Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                            </div>
                                        </div>
                                        ";
        }
        // line 365
        yield "
                                        <div class=\"invalid-feedback\">
                                            Please upload a thumbnail image.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                            Upload a 300x200px thumbnail image (JPEG, PNG)
                                        </small>
                                    </div>
                                </div>

                                <!-- Banner Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"banner_image\" class=\"form-label\">
                                            <i class=\"fas fa-panorama\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Banner Image <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"file\"
                                               class=\"form-control enhanced-file-field\"
                                               id=\"banner_image\"
                                               name=\"banner_image\"
                                               accept=\"image/jpeg,image/png,image/jpg\"
                                               ";
        // line 388
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 388, $this->source); })()), "bannerImage", [], "any", false, false, false, 388)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("") : ("required"));
        yield "
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">

                                        ";
        // line 391
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 391, $this->source); })()), "bannerImage", [], "any", false, false, false, 391)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 392
            yield "                                        <div class=\"image-preview mt-3 text-center\" id=\"banner-preview\">
                                            <div class=\"professional-banner-container mx-auto\" style=\"width: 100%; max-width: 500px; height: 200px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                                <img src=\"";
            // line 394
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/courses/banners/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 394, $this->source); })()), "bannerImage", [], "any", false, false, false, 394))), "html", null, true);
            yield "\" alt=\"Current Banner\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                            </div>
                                            <small class=\"text-muted mt-2 d-block\">Current banner</small>
                                        </div>
                                        ";
        } else {
            // line 399
            yield "                                        <div class=\"image-preview mt-3 text-center\" id=\"banner-preview\" style=\"display: none;\">
                                            <div class=\"professional-banner-container mx-auto\" style=\"width: 100%; max-width: 500px; height: 200px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                                <img src=\"\" alt=\"Banner Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                            </div>
                                        </div>
                                        ";
        }
        // line 405
        yield "
                                        <div class=\"invalid-feedback\">
                                            Please upload a banner image.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                            Upload a 1200x400px banner image (JPEG, PNG)
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Has Modules -->
                            <div class=\"form-group\" style=\"margin-bottom: 1.5rem;\">
                                <div class=\"form-check form-switch\" style=\"padding-left: 0; margin-left: 0;\">
                                    <input type=\"checkbox\"
                                           class=\"form-check-input\"
                                           id=\"has_modules\"
                                           name=\"has_modules\"
                                           value=\"1\"
                                           ";
        // line 425
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 425, $this->source); })()), "hasModules", [], "any", false, false, false, 425)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("checked") : (""));
        yield "
                                           style=\"transform: scale(1.2); margin-left: 0;\">
                                    <label class=\"form-check-label\" for=\"has_modules\" style=\"margin-left: 2.5rem; padding-top: 0.125rem;\">
                                        <i class=\"fas fa-list\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        <strong>Enable Course Modules</strong>
                                    </label>
                                </div>
                            </div>

                            <!-- Course Modules Management Section -->
                            <div id=\"modules-section\" class=\"form-group\" style=\"display: ";
        // line 435
        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 435, $this->source); })()), "hasModules", [], "any", false, false, false, 435)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("block") : ("none"));
        yield ";\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-puzzle-piece\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Course Modules
                                </label>

                                <div id=\"modules-container\">
                                    ";
        // line 442
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 442, $this->source); })()), "modules", [], "any", false, false, false, 442) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 442, $this->source); })()), "modules", [], "any", false, false, false, 442)) > 0))) {
            // line 443
            yield "                                        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 443, $this->source); })()), "modules", [], "any", false, false, false, 443));
            $context['loop'] = [
              'parent' => $context['_parent'],
              'index0' => 0,
              'index'  => 1,
              'first'  => true,
            ];
            if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                $length = count($context['_seq']);
                $context['loop']['revindex0'] = $length - 1;
                $context['loop']['revindex'] = $length;
                $context['loop']['length'] = $length;
                $context['loop']['last'] = 1 === $length;
            }
            foreach ($context['_seq'] as $context["_key"] => $context["module"]) {
                // line 444
                yield "                                        <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                                            <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                                                <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                                                    <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                                                    Module <span class=\"module-number\">";
                // line 448
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 448), "html", null, true);
                yield "</span>
                                                </h6>
                                                <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px; padding: 0; display: flex; align-items: center; justify-content: center;\">
                                                    <i class=\"fas fa-trash\" style=\"font-size: 0.8rem;\"></i>
                                                </button>
                                            </div>

                                            <div class=\"module-content\">
                                                <!-- Module Code and Title -->
                                                <div class=\"row\">
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Module Code
                                                            </label>
                                                            <input type=\"text\" class=\"form-control module-code\" name=\"modules[";
                // line 464
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 464), "html", null, true);
                yield "][code]\" value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "code", [], "any", false, false, false, 464), "html", null, true);
                yield "\" placeholder=\"e.g., Module-";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 464), "html", null, true);
                yield "\" style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                        </div>
                                                    </div>
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Module Title <span class=\"text-danger\">*</span>
                                                            </label>
                                                            <input type=\"text\" class=\"form-control module-title\" name=\"modules[";
                // line 473
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 473), "html", null, true);
                yield "][title]\" value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "title", [], "any", false, false, false, 473), "html", null, true);
                yield "\" placeholder=\"e.g., Introduction to Trading Fundamentals\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Module Description -->
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Module Description <span class=\"text-danger\">*</span>
                                                    </label>
                                                    <textarea class=\"form-control module-description\" name=\"modules[";
                // line 484
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 484), "html", null, true);
                yield "][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 150px; border: 2px solid #ced4da; border-radius: 0.375rem;\">";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "description", [], "any", false, false, false, 484), "html", null, true);
                yield "</textarea>
                                                </div>

                                                <!-- Module Duration and Price -->
                                                <div class=\"row\">
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Duration (Minutes) <span class=\"text-danger\">*</span>
                                                            </label>
                                                            <input type=\"number\" class=\"form-control module-duration\" name=\"modules[";
                // line 495
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 495), "html", null, true);
                yield "][duration]\" value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "duration", [], "any", false, false, false, 495), "html", null, true);
                yield "\" placeholder=\"e.g., 60\" min=\"1\" max=\"1000\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                        </div>
                                                    </div>
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Price (USD) <span class=\"text-danger\">*</span>
                                                            </label>
                                                            <input type=\"number\" class=\"form-control module-price\" name=\"modules[";
                // line 504
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 504), "html", null, true);
                yield "][price]\" value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "price", [], "any", false, false, false, 504), "html", null, true);
                yield "\" placeholder=\"0.00\" step=\"0.01\" min=\"0\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Module Learning Outcomes -->
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Learning Outcomes <span class=\"text-danger\">*</span>
                                                    </label>
                                                    <div class=\"learning-outcomes-container\">
                                                        ";
                // line 516
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 516) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 516)) > 0))) {
                    // line 517
                    yield "                                                            ";
                    $context['_parent'] = $context;
                    $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "learningOutcomes", [], "any", false, false, false, 517));
                    $context['loop'] = [
                      'parent' => $context['_parent'],
                      'index0' => 0,
                      'index'  => 1,
                      'first'  => true,
                    ];
                    if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                        $length = count($context['_seq']);
                        $context['loop']['revindex0'] = $length - 1;
                        $context['loop']['revindex'] = $length;
                        $context['loop']['length'] = $length;
                        $context['loop']['last'] = 1 === $length;
                    }
                    foreach ($context['_seq'] as $context["_key"] => $context["outcome"]) {
                        // line 518
                        yield "                                                            <div class=\"input-group mb-2\">
                                                                <input type=\"text\" class=\"form-control\" name=\"modules[";
                        // line 519
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "parent", [], "any", false, false, false, 519), "loop", [], "any", false, false, false, 519), "index0", [], "any", false, false, false, 519), "html", null, true);
                        yield "][learning_outcomes][]\" value=\"";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["outcome"], "html", null, true);
                        yield "\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                                <div class=\"input-group-append\">
                                                                    ";
                        // line 521
                        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "first", [], "any", false, false, false, 521)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                            // line 522
                            yield "                                                                    <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                        <i class=\"fas fa-plus\"></i>
                                                                    </button>
                                                                    ";
                        } else {
                            // line 526
                            yield "                                                                    <button type=\"button\" class=\"btn btn-danger remove-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                                        <i class=\"fas fa-minus\"></i>
                                                                    </button>
                                                                    <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                        <i class=\"fas fa-plus\"></i>
                                                                    </button>
                                                                    ";
                        }
                        // line 533
                        yield "                                                                </div>
                                                            </div>
                                                            ";
                        ++$context['loop']['index0'];
                        ++$context['loop']['index'];
                        $context['loop']['first'] = false;
                        if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                            --$context['loop']['revindex0'];
                            --$context['loop']['revindex'];
                            $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                        }
                    }
                    $_parent = $context['_parent'];
                    unset($context['_seq'], $context['_key'], $context['outcome'], $context['_parent'], $context['loop']);
                    $context = array_intersect_key($context, $_parent) + $_parent;
                    // line 536
                    yield "                                                        ";
                } else {
                    // line 537
                    yield "                                                        <div class=\"input-group mb-2\">
                                                            <input type=\"text\" class=\"form-control\" name=\"modules[";
                    // line 538
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 538), "html", null, true);
                    yield "][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                            <div class=\"input-group-append\">
                                                                <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                    <i class=\"fas fa-plus\"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        ";
                }
                // line 546
                yield "                                                    </div>
                                                </div>

                                                <!-- Module Features -->
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Features <span class=\"text-danger\">*</span>
                                                    </label>
                                                    <div class=\"features-container\">
                                                        ";
                // line 556
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["module"], "features", [], "any", false, false, false, 556) && (Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["module"], "features", [], "any", false, false, false, 556)) > 0))) {
                    // line 557
                    yield "                                                            ";
                    $context['_parent'] = $context;
                    $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, $context["module"], "features", [], "any", false, false, false, 557));
                    $context['loop'] = [
                      'parent' => $context['_parent'],
                      'index0' => 0,
                      'index'  => 1,
                      'first'  => true,
                    ];
                    if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
                        $length = count($context['_seq']);
                        $context['loop']['revindex0'] = $length - 1;
                        $context['loop']['revindex'] = $length;
                        $context['loop']['length'] = $length;
                        $context['loop']['last'] = 1 === $length;
                    }
                    foreach ($context['_seq'] as $context["_key"] => $context["feature"]) {
                        // line 558
                        yield "                                                            <div class=\"input-group mb-2\">
                                                                <input type=\"text\" class=\"form-control\" name=\"modules[";
                        // line 559
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "parent", [], "any", false, false, false, 559), "loop", [], "any", false, false, false, 559), "index0", [], "any", false, false, false, 559), "html", null, true);
                        yield "][features][]\" value=\"";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["feature"], "html", null, true);
                        yield "\" placeholder=\"e.g., Interactive trading simulator\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                                <div class=\"input-group-append\">
                                                                    ";
                        // line 561
                        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "first", [], "any", false, false, false, 561)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                            // line 562
                            yield "                                                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                        <i class=\"fas fa-plus\"></i>
                                                                    </button>
                                                                    ";
                        } else {
                            // line 566
                            yield "                                                                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                                        <i class=\"fas fa-minus\"></i>
                                                                    </button>
                                                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                        <i class=\"fas fa-plus\"></i>
                                                                    </button>
                                                                    ";
                        }
                        // line 573
                        yield "                                                                </div>
                                                            </div>
                                                            ";
                        ++$context['loop']['index0'];
                        ++$context['loop']['index'];
                        $context['loop']['first'] = false;
                        if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                            --$context['loop']['revindex0'];
                            --$context['loop']['revindex'];
                            $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                        }
                    }
                    $_parent = $context['_parent'];
                    unset($context['_seq'], $context['_key'], $context['feature'], $context['_parent'], $context['loop']);
                    $context = array_intersect_key($context, $_parent) + $_parent;
                    // line 576
                    yield "                                                        ";
                } else {
                    // line 577
                    yield "                                                        <div class=\"input-group mb-2\">
                                                            <input type=\"text\" class=\"form-control\" name=\"modules[";
                    // line 578
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 578), "html", null, true);
                    yield "][features][]\" placeholder=\"e.g., Interactive trading simulator\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                            <div class=\"input-group-append\">
                                                                <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                    <i class=\"fas fa-plus\"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        ";
                }
                // line 586
                yield "                                                    </div>
                                                </div>

                                                <!-- Hidden fields -->
                                                <input type=\"hidden\" name=\"modules[";
                // line 590
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 590), "html", null, true);
                yield "][is_active]\" value=\"1\">
                                                <input type=\"hidden\" name=\"modules[";
                // line 591
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index0", [], "any", false, false, false, 591), "html", null, true);
                yield "][sort_order]\" value=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "index", [], "any", false, false, false, 591), "html", null, true);
                yield "\">
                                            </div>
                                        </div>
                                        ";
                ++$context['loop']['index0'];
                ++$context['loop']['index'];
                $context['loop']['first'] = false;
                if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                    --$context['loop']['revindex0'];
                    --$context['loop']['revindex'];
                    $context['loop']['last'] = 0 === $context['loop']['revindex0'];
                }
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['module'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 595
            yield "                                    ";
        } else {
            // line 596
            yield "                                    <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                                        <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                                            <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                                                <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                                                Module <span class=\"module-number\">1</span>
                                            </h6>
                                            <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px; padding: 0; display: flex; align-items: center; justify-content: center;\">
                                                <i class=\"fas fa-trash\" style=\"font-size: 0.8rem;\"></i>
                                            </button>
                                        </div>

                                        <div class=\"module-content\">
                                            <!-- Module Code and Title -->
                                            <div class=\"row\">
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Module Code
                                                        </label>
                                                        <input type=\"text\" class=\"form-control module-code\" name=\"modules[0][code]\" placeholder=\"e.g., Module-1\" style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                    </div>
                                                </div>
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Module Title <span class=\"text-danger\">*</span>
                                                        </label>
                                                        <input type=\"text\" class=\"form-control module-title\" name=\"modules[0][title]\" placeholder=\"e.g., Introduction to Trading Fundamentals\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Description -->
                                            <div class=\"form-group\">
                                                <label class=\"form-label\">
                                                    <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                    Module Description <span class=\"text-danger\">*</span>
                                                </label>
                                                <textarea class=\"form-control module-description\" name=\"modules[0][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 100px; border: 2px solid #ced4da; border-radius: 0.375rem;\"></textarea>
                                            </div>

                                            <!-- Module Duration and Price -->
                                            <div class=\"row\">
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Duration (Minutes) <span class=\"text-danger\">*</span>
                                                        </label>
                                                        <input type=\"number\" class=\"form-control module-duration\" name=\"modules[0][duration]\" placeholder=\"e.g., 60\" min=\"1\" max=\"1000\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                    </div>
                                                </div>
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Price (USD) <span class=\"text-danger\">*</span>
                                                        </label>
                                                        <input type=\"number\" class=\"form-control module-price\" name=\"modules[0][price]\" placeholder=\"0.00\" step=\"0.01\" min=\"0\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Learning Outcomes -->
                                            <div class=\"form-group\">
                                                <label class=\"form-label\">
                                                    <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                    Learning Outcomes <span class=\"text-danger\">*</span>
                                                </label>
                                                <div class=\"learning-outcomes-container\">
                                                    <div class=\"input-group mb-2\">
                                                        <input type=\"text\" class=\"form-control\" name=\"modules[0][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                        <div class=\"input-group-append\">
                                                            <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                <i class=\"fas fa-plus\"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Features -->
                                            <div class=\"form-group\">
                                                <label class=\"form-label\">
                                                    <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                    Features <span class=\"text-danger\">*</span>
                                                </label>
                                                <div class=\"features-container\">
                                                    <div class=\"input-group mb-2\">
                                                        <input type=\"text\" class=\"form-control\" name=\"modules[0][features][]\" placeholder=\"e.g., Interactive trading simulator\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                        <div class=\"input-group-append\">
                                                            <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                <i class=\"fas fa-plus\"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Hidden fields -->
                                            <input type=\"hidden\" name=\"modules[0][is_active]\" value=\"1\">
                                            <input type=\"hidden\" name=\"modules[0][sort_order]\" value=\"1\">
                                        </div>
                                    </div>
                                    ";
        }
        // line 703
        yield "                                </div>

                                <button type=\"button\" id=\"add-module\" class=\"btn btn-outline-primary\">
                                    <i class=\"fas fa-plus\" style=\"margin-right: 0.5rem;\"></i>
                                    Add Another Module
                                </button>
                            </div>

                        </div>

                        <!-- Action Buttons Section -->
                        <div class=\"d-flex justify-content-between align-items-center flex-wrap gap-3\">
                                        <button type=\"submit\" class=\"btn btn-lg btn-success\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; transition: all 0.3s ease; min-width: 200px;\">
                                            <i class=\"fas fa-save mr-2\"></i>
                                            Update Course
                                        </button>
                                        <a href=\"";
        // line 719
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses");
        yield "\" class=\"btn btn-lg btn-secondary\" style=\"font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; min-width: 200px; background: #6c757d; border-color: #6c757d;\">
                                            <i class=\"fas fa-times mr-2\"></i>
                                            Cancel
                                        </a>
                        </div>
                    </div>
                </div>

            </div>
        </form>
    </div>
</div>

<!-- Professional Module Removal Modal -->
<div class=\"modal fade\" id=\"courseValidationModal\" tabindex=\"-1\" aria-labelledby=\"courseValidationModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered\">
        <div class=\"modal-content\" style=\"border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #dc3545 100%); color: white; border: none; border-radius: 15px 15px 0 0; padding: 1.5rem;\">
                <h5 class=\"modal-title\" id=\"courseValidationModalLabel\" style=\"font-weight: 600;\">
                    <i class=\"fas fa-exclamation-triangle me-2\"></i>
                    Module Required
                </h5>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                <p class=\"mb-3\" style=\"color: #a90418;\">
                    At least one module is required.
                </p>
                <small class=\"text-muted\">Please add a module or disable the \"Enable Course Modules\" option.</small>
            </div>
            <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                <button type=\"button\" class=\"btn btn-primary btn-sm\" data-bs-dismiss=\"modal\" style=\"transition: all 0.3s ease;\">
                    <i class=\"fas fa-check me-1\"></i>OK
                </button>
            </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 759
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 760
        yield "<script>
\$(document).ready(function() {
    // Function to toggle module field validation based on \"Course Has Modules\" state
    function toggleModuleValidation() {
        var hasModules = \$('#has_modules').is(':checked');
        var moduleInputs = \$('#modules-section input[name*=\"modules\"], #modules-section textarea[name*=\"modules\"]');

        if (hasModules) {
            // Enable validation for module fields
            moduleInputs.each(function() {
                \$(this).attr('required', true);
            });
        } else {
            // Disable validation for module fields
            moduleInputs.each(function() {
                \$(this).removeAttr('required');
                // Clear any validation state
                \$(this).removeClass('is-invalid is-valid');
                \$(this).closest('.form-group').find('.invalid-feedback').hide();
            });
        }
    }

    // Initialize validation state
    toggleModuleValidation();

    // Image preview functionality
    function setupImagePreview(inputId, previewId) {
        \$('#' + inputId).on('change', function(e) {
            const file = e.target.files[0];
            const preview = \$('#' + previewId);
            const img = preview.find('img');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    img.attr('src', e.target.result);
                    preview.show();
                };
                reader.readAsDataURL(file);
            } else {
                preview.hide();
            }
        });
    }

    // Setup image previews
    setupImagePreview('thumbnail_image', 'thumbnail-preview');
    setupImagePreview('banner_image', 'banner-preview');

    // Learning outcomes management
    \$(document).on('click', '.add-outcome', function() {
        var container = \$(this).closest('#learning-outcomes-container, .learning-outcomes-container');
        var isModuleOutcome = container.hasClass('learning-outcomes-container');
        var moduleIndex = isModuleOutcome ? container.closest('.module-item').index() : '';
        var namePrefix = isModuleOutcome ? `modules[\${moduleIndex}][learning_outcomes][]` : 'learning_outcomes[]';

        var outcomeHtml = `
            <div class=\"input-group mb-2\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"\${namePrefix}\" placeholder=\"Enter another learning outcome...\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(outcomeHtml);
        toggleModuleValidation();
    });

    \$(document).on('click', '.remove-outcome', function() {
        \$(this).closest('.input-group').remove();
    });

    // Features management
    \$(document).on('click', '.add-feature', function() {
        var container = \$(this).closest('#features-container, .features-container');
        var isModuleFeature = container.hasClass('features-container');
        var moduleIndex = isModuleFeature ? container.closest('.module-item').index() : '';
        var namePrefix = isModuleFeature ? `modules[\${moduleIndex}][features][]` : 'features[]';

        var featureHtml = `
            <div class=\"input-group mb-2\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"\${namePrefix}\" placeholder=\"Enter another feature...\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(featureHtml);
        toggleModuleValidation();
    });

    \$(document).on('click', '.remove-feature', function() {
        \$(this).closest('.input-group').remove();
    });

    // Module management
    var moduleIndex = \$('.module-item').length;

    // Toggle module section visibility
    \$('#has_modules').on('change', function() {
        if (\$(this).is(':checked')) {
            \$('#modules-section').slideDown();
        } else {
            \$('#modules-section').slideUp();
        }
        toggleModuleValidation();
    });

    // Add new module
    \$('#add-module').on('click', function() {
        var newModule = `
            <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                    <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                        <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                        Module <span class=\"module-number\">\${moduleIndex + 1}</span>
                    </h6>
                    <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px; padding: 0; display: flex; align-items: center; justify-content: center;\">
                        <i class=\"fas fa-trash\" style=\"font-size: 0.8rem;\"></i>
                    </button>
                </div>

                <div class=\"module-content\">
                    <!-- Module Code and Title -->
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Module Code
                                </label>
                                <input type=\"text\" class=\"form-control module-code\" name=\"modules[\${moduleIndex}][code]\" placeholder=\"e.g., Module-\${moduleIndex + 1}\" style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Module Title <span class=\"text-danger\">*</span>
                                </label>
                                <input type=\"text\" class=\"form-control module-title\" name=\"modules[\${moduleIndex}][title]\" placeholder=\"e.g., Advanced Trading Strategies\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                            </div>
                        </div>
                    </div>

                    <!-- Module Description -->
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                            Module Description <span class=\"text-danger\">*</span>
                        </label>
                        <textarea class=\"form-control module-description\" name=\"modules[\${moduleIndex}][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 150px; border: 2px solid #ced4da; border-radius: 0.375rem;\"></textarea>
                    </div>

                    <!-- Module Duration and Price -->
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Duration (Minutes) <span class=\"text-danger\">*</span>
                                </label>
                                <input type=\"number\" class=\"form-control module-duration\" name=\"modules[\${moduleIndex}][duration]\" placeholder=\"e.g., 90\" min=\"1\" max=\"1000\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Price (USD) <span class=\"text-danger\">*</span>
                                </label>
                                <input type=\"number\" class=\"form-control module-price\" name=\"modules[\${moduleIndex}][price]\" placeholder=\"0.00\" step=\"0.01\" min=\"0\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                            </div>
                        </div>
                    </div>

                    <!-- Module Learning Outcomes -->
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                            Learning Outcomes <span class=\"text-danger\">*</span>
                        </label>
                        <div class=\"learning-outcomes-container\">
                            <div class=\"input-group mb-2\">
                                <input type=\"text\" class=\"form-control\" name=\"modules[\${moduleIndex}][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                <div class=\"input-group-append\">
                                    <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                        <i class=\"fas fa-plus\"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Module Features -->
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                            Features <span class=\"text-danger\">*</span>
                        </label>
                        <div class=\"features-container\">
                            <div class=\"input-group mb-2\">
                                <input type=\"text\" class=\"form-control\" name=\"modules[\${moduleIndex}][features][]\" placeholder=\"e.g., Interactive trading simulator\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                <div class=\"input-group-append\">
                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                        <i class=\"fas fa-plus\"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden fields -->
                    <input type=\"hidden\" name=\"modules[\${moduleIndex}][is_active]\" value=\"1\">
                    <input type=\"hidden\" name=\"modules[\${moduleIndex}][sort_order]\" value=\"\${moduleIndex + 1}\">
                </div>
            </div>
        `;

        \$('#modules-container').append(newModule);
        moduleIndex++;
        updateModuleNumbers();
        toggleModuleValidation();
    });

    // Remove module
    \$(document).on('click', '.remove-module', function() {
        \$(this).closest('.module-item').remove();
        updateModuleNumbers();
        toggleModuleValidation();
    });

    // Update module numbers and form field names
    function updateModuleNumbers() {
        \$('.module-item').each(function(index) {
            \$(this).find('.module-number').text(index + 1);
            // Update form field names
            \$(this).find('input, textarea, select').each(function() {
                var name = \$(this).attr('name');
                if (name && name.includes('modules[')) {
                    var newName = name.replace(/modules\\[\\d+\\]/, 'modules[' + index + ']');
                    \$(this).attr('name', newName);
                }
            });
        });
    }

    // Form validation
    \$('#course-form').on('submit', function(e) {
        var hasModules = \$('#has_modules').is(':checked');
        var moduleCount = \$('.module-item').length;

        if (hasModules && moduleCount === 0) {
            e.preventDefault();
            const modal = new bootstrap.Modal(document.getElementById('courseValidationModal'));
            modal.show();
            return false;
        }

        // Update module validation before form submission
        toggleModuleValidation();

        // Validate all required fields
        var isValid = true;
        \$(this).find('input[required], textarea[required], select[required]').each(function() {
            if (!\$(this).val()) {
                isValid = false;
                \$(this).addClass('is-invalid');
            } else {
                \$(this).removeClass('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/courses/edit.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  1300 => 760,  1287 => 759,  1237 => 719,  1219 => 703,  1110 => 596,  1107 => 595,  1087 => 591,  1083 => 590,  1077 => 586,  1066 => 578,  1063 => 577,  1060 => 576,  1044 => 573,  1035 => 566,  1029 => 562,  1027 => 561,  1020 => 559,  1017 => 558,  999 => 557,  997 => 556,  985 => 546,  974 => 538,  971 => 537,  968 => 536,  952 => 533,  943 => 526,  937 => 522,  935 => 521,  928 => 519,  925 => 518,  907 => 517,  905 => 516,  888 => 504,  874 => 495,  858 => 484,  842 => 473,  826 => 464,  807 => 448,  801 => 444,  783 => 443,  781 => 442,  771 => 435,  758 => 425,  736 => 405,  728 => 399,  720 => 394,  716 => 392,  714 => 391,  708 => 388,  683 => 365,  675 => 359,  667 => 354,  663 => 352,  661 => 351,  655 => 348,  632 => 327,  616 => 313,  613 => 312,  597 => 309,  588 => 302,  582 => 298,  580 => 297,  572 => 292,  566 => 288,  548 => 287,  546 => 286,  530 => 272,  514 => 258,  511 => 257,  495 => 254,  486 => 247,  480 => 243,  478 => 242,  470 => 237,  464 => 233,  446 => 232,  444 => 231,  419 => 209,  389 => 182,  365 => 160,  356 => 157,  349 => 156,  345 => 155,  317 => 130,  286 => 102,  257 => 76,  236 => 58,  232 => 57,  215 => 43,  199 => 29,  189 => 25,  186 => 24,  182 => 23,  179 => 22,  169 => 18,  166 => 17,  162 => 16,  158 => 14,  145 => 13,  131 => 9,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Edit Course - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Course{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_courses') }}\">Courses</a></li>
<li class=\"breadcrumb-item active\">Edit Course</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Course
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Courses Button -->
                        <a href=\"{{ path('admin_courses') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method=\"post\" id=\"course-form\" class=\"needs-validation\" enctype=\"multipart/form-data\" novalidate>
            <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('course_edit') }}\">
            <input type=\"hidden\" name=\"is_active\" value=\"{{ course.isActive ? '1' : '0' }}\">
            <div class=\"card-body\">
                    <!-- Single Column Layout -->
                    <div class=\"row\">
                        <div class=\"col-12\">
                            <!-- Course Code and Title Row -->
                            <div class=\"row\">
                                <!-- Course Code -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"code\" class=\"form-label\">
                                            <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Code <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"code\"
                                               name=\"code\"
                                               value=\"{{ course.code }}\"
                                               placeholder=\"e.g., TRAD101, FIN200\"
                                               required
                                               maxlength=\"10\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid course code.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                            Course code should be unique and descriptive (e.g., TRAD101, FIN200)
                                        </small>
                                    </div>
                                </div>

                                <!-- Course Title -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"title\" class=\"form-label\">
                                            <i class=\"fas fa-book\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Course Title <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               id=\"title\"
                                               name=\"title\"
                                               value=\"{{ course.title }}\"
                                               placeholder=\"Enter course title\"
                                               required
                                               maxlength=\"255\"
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid course title.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                            Enter a descriptive title for your course
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Course Description -->
                            <div class=\"form-group\">
                                <label for=\"description\" class=\"form-label\">
                                    <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Course Description <span class=\"text-danger\">*</span>
                                </label>
                                <textarea class=\"form-control enhanced-field\"
                                          id=\"description\"
                                          name=\"description\"
                                          rows=\"8\"
                                          placeholder=\"Enter detailed course description...\"
                                          required
                                          style=\"min-height: 150px; border: 2px solid #ced4da; border-radius: 0.375rem;\">{{ course.description }}</textarea>
                                <div class=\"invalid-feedback\">
                                    Please provide a course description.
                                </div>
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                    Provide a comprehensive description of what students will learn
                                </small>
                            </div>

                            <!-- Category and Duration Row -->
                            <div class=\"row\">
                                <!-- Category -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"category\" class=\"form-label\">
                                            <i class=\"fas fa-tags\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Category <span class=\"text-danger\">*</span>
                                        </label>
                                        <select class=\"form-control enhanced-field\"
                                                id=\"category\"
                                                name=\"category_id\"
                                                required
                                                style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                            <option value=\"\">Select a category</option>
                                            {% for category in categories %}
                                                <option value=\"{{ category.id }}\" {{ course.category and course.category == category.name ? 'selected' : '' }}>
                                                    {{ category.name }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                        <div class=\"invalid-feedback\">
                                            Please select a category.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                            Choose the most appropriate category for your course
                                        </small>
                                    </div>
                                </div>

                                <!-- Duration -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"duration\" class=\"form-label\">
                                            <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Duration (Minutes) <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"number\"
                                               class=\"form-control enhanced-field\"
                                               id=\"duration\"
                                               name=\"duration\"
                                               value=\"{{ course.duration }}\"
                                               placeholder=\"e.g., 120\"
                                               min=\"1\"
                                               max=\"10000\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                        <div class=\"invalid-feedback\">
                                            Please provide a valid duration.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                            Enter the total duration in minutes
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Price -->
                            <div class=\"form-group\">
                                <label for=\"price\" class=\"form-label\">
                                    <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Price (USD) <span class=\"text-danger\">*</span>
                                </label>
                                <input type=\"number\"
                                       class=\"form-control enhanced-field\"
                                       id=\"price\"
                                       name=\"price\"
                                       value=\"{{ course.price }}\"
                                       placeholder=\"0.00\"
                                       step=\"0.01\"
                                       min=\"0\"
                                       required
                                       style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;\">
                                <div class=\"invalid-feedback\">
                                    Please provide a valid price.
                                </div>
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                    Set the course price in USD (use 0 for free courses)
                                </small>
                            </div>

                            <!-- Learning Outcomes -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Learning Outcomes <span class=\"text-danger\">*</span>
                                </label>
                                <div id=\"learning-outcomes-container\">
                                    {% if course.learningOutcomes and course.learningOutcomes|length > 0 %}
                                        {% for outcome in course.learningOutcomes %}
                                        <div class=\"input-group mb-2\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"learning_outcomes[]\"
                                                   value=\"{{ outcome }}\"
                                                   placeholder=\"e.g., Master advanced chart analysis techniques\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                {% if loop.first %}
                                                <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                                {% else %}
                                                <button type=\"button\" class=\"btn btn-danger remove-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                    <i class=\"fas fa-minus\"></i>
                                                </button>
                                                <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                    <div class=\"input-group mb-2\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"learning_outcomes[]\"
                                               placeholder=\"e.g., Master advanced chart analysis techniques\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                    Add specific learning outcomes that students will achieve
                                </small>
                            </div>

                            <!-- Features -->
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Features <span class=\"text-danger\">*</span>
                                </label>
                                <div id=\"features-container\">
                                    {% if course.features and course.features|length > 0 %}
                                        {% for feature in course.features %}
                                        <div class=\"input-group mb-2\">
                                            <input type=\"text\"
                                                   class=\"form-control enhanced-field\"
                                                   name=\"features[]\"
                                                   value=\"{{ feature }}\"
                                                   placeholder=\"e.g., Interactive trading simulator\"
                                                   required
                                                   style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                            <div class=\"input-group-append\">
                                                {% if loop.first %}
                                                <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                                {% else %}
                                                <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                    <i class=\"fas fa-minus\"></i>
                                                </button>
                                                <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                    <i class=\"fas fa-plus\"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                    <div class=\"input-group mb-2\">
                                        <input type=\"text\"
                                               class=\"form-control enhanced-field\"
                                               name=\"features[]\"
                                               placeholder=\"e.g., Interactive trading simulator\"
                                               required
                                               style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                        <div class=\"input-group-append\">
                                            <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                <i class=\"fas fa-plus\"></i>
                                            </button>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                                <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                    <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                    Highlight key features and benefits of your course
                                </small>
                            </div>

                            <!-- Image Uploads Section -->
                            <div class=\"row\">
                                <!-- Thumbnail Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"thumbnail_image\" class=\"form-label\">
                                            <i class=\"fas fa-image\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Thumbnail Image <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"file\"
                                               class=\"form-control enhanced-file-field\"
                                               id=\"thumbnail_image\"
                                               name=\"thumbnail_image\"
                                               accept=\"image/jpeg,image/png,image/jpg\"
                                               {{ course.thumbnailImage ? '' : 'required' }}
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">

                                        {% if course.thumbnailImage %}
                                        <div class=\"image-preview mt-3 text-center\" id=\"thumbnail-preview\">
                                            <div class=\"professional-thumbnail-container mx-auto\" style=\"width: 100%; max-width: 300px; height: 200px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                                <img src=\"{{ asset('uploads/courses/thumbnails/' ~ course.thumbnailImage) }}\" alt=\"Current Thumbnail\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                            </div>
                                            <small class=\"text-muted mt-2 d-block\">Current thumbnail</small>
                                        </div>
                                        {% else %}
                                        <div class=\"image-preview mt-3 text-center\" id=\"thumbnail-preview\" style=\"display: none;\">
                                            <div class=\"professional-thumbnail-container mx-auto\" style=\"width: 100%; max-width: 300px; height: 200px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                                <img src=\"\" alt=\"Thumbnail Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                            </div>
                                        </div>
                                        {% endif %}

                                        <div class=\"invalid-feedback\">
                                            Please upload a thumbnail image.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                            Upload a 300x200px thumbnail image (JPEG, PNG)
                                        </small>
                                    </div>
                                </div>

                                <!-- Banner Image -->
                                <div class=\"col-md-6\">
                                    <div class=\"form-group\">
                                        <label for=\"banner_image\" class=\"form-label\">
                                            <i class=\"fas fa-panorama\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                            Banner Image <span class=\"text-danger\">*</span>
                                        </label>
                                        <input type=\"file\"
                                               class=\"form-control enhanced-file-field\"
                                               id=\"banner_image\"
                                               name=\"banner_image\"
                                               accept=\"image/jpeg,image/png,image/jpg\"
                                               {{ course.bannerImage ? '' : 'required' }}
                                               style=\"height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;\">

                                        {% if course.bannerImage %}
                                        <div class=\"image-preview mt-3 text-center\" id=\"banner-preview\">
                                            <div class=\"professional-banner-container mx-auto\" style=\"width: 100%; max-width: 500px; height: 200px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                                <img src=\"{{ asset('uploads/courses/banners/' ~ course.bannerImage) }}\" alt=\"Current Banner\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                            </div>
                                            <small class=\"text-muted mt-2 d-block\">Current banner</small>
                                        </div>
                                        {% else %}
                                        <div class=\"image-preview mt-3 text-center\" id=\"banner-preview\" style=\"display: none;\">
                                            <div class=\"professional-banner-container mx-auto\" style=\"width: 100%; max-width: 500px; height: 200px; border: 2px solid #1e3c72; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 2px 8px rgba(0,0,0,0.1);\">
                                                <img src=\"\" alt=\"Banner Preview\" class=\"w-100 h-100\" style=\"object-fit: cover;\">
                                            </div>
                                        </div>
                                        {% endif %}

                                        <div class=\"invalid-feedback\">
                                            Please upload a banner image.
                                        </div>
                                        <small class=\"form-text text-muted help-text\" style=\"display: none;\">
                                            <i class=\"fas fa-info-circle text-info mr-1\"></i>
                                            Upload a 1200x400px banner image (JPEG, PNG)
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Has Modules -->
                            <div class=\"form-group\" style=\"margin-bottom: 1.5rem;\">
                                <div class=\"form-check form-switch\" style=\"padding-left: 0; margin-left: 0;\">
                                    <input type=\"checkbox\"
                                           class=\"form-check-input\"
                                           id=\"has_modules\"
                                           name=\"has_modules\"
                                           value=\"1\"
                                           {{ course.hasModules ? 'checked' : '' }}
                                           style=\"transform: scale(1.2); margin-left: 0;\">
                                    <label class=\"form-check-label\" for=\"has_modules\" style=\"margin-left: 2.5rem; padding-top: 0.125rem;\">
                                        <i class=\"fas fa-list\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                        <strong>Enable Course Modules</strong>
                                    </label>
                                </div>
                            </div>

                            <!-- Course Modules Management Section -->
                            <div id=\"modules-section\" class=\"form-group\" style=\"display: {{ course.hasModules ? 'block' : 'none' }};\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-puzzle-piece\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Course Modules
                                </label>

                                <div id=\"modules-container\">
                                    {% if course.modules and course.modules|length > 0 %}
                                        {% for module in course.modules %}
                                        <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                                            <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                                                <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                                                    <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                                                    Module <span class=\"module-number\">{{ loop.index }}</span>
                                                </h6>
                                                <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px; padding: 0; display: flex; align-items: center; justify-content: center;\">
                                                    <i class=\"fas fa-trash\" style=\"font-size: 0.8rem;\"></i>
                                                </button>
                                            </div>

                                            <div class=\"module-content\">
                                                <!-- Module Code and Title -->
                                                <div class=\"row\">
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Module Code
                                                            </label>
                                                            <input type=\"text\" class=\"form-control module-code\" name=\"modules[{{ loop.index0 }}][code]\" value=\"{{ module.code }}\" placeholder=\"e.g., Module-{{ loop.index }}\" style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                        </div>
                                                    </div>
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Module Title <span class=\"text-danger\">*</span>
                                                            </label>
                                                            <input type=\"text\" class=\"form-control module-title\" name=\"modules[{{ loop.index0 }}][title]\" value=\"{{ module.title }}\" placeholder=\"e.g., Introduction to Trading Fundamentals\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Module Description -->
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Module Description <span class=\"text-danger\">*</span>
                                                    </label>
                                                    <textarea class=\"form-control module-description\" name=\"modules[{{ loop.index0 }}][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 150px; border: 2px solid #ced4da; border-radius: 0.375rem;\">{{ module.description }}</textarea>
                                                </div>

                                                <!-- Module Duration and Price -->
                                                <div class=\"row\">
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Duration (Minutes) <span class=\"text-danger\">*</span>
                                                            </label>
                                                            <input type=\"number\" class=\"form-control module-duration\" name=\"modules[{{ loop.index0 }}][duration]\" value=\"{{ module.duration }}\" placeholder=\"e.g., 60\" min=\"1\" max=\"1000\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                        </div>
                                                    </div>
                                                    <div class=\"col-md-6\">
                                                        <div class=\"form-group\">
                                                            <label class=\"form-label\">
                                                                <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                                Price (USD) <span class=\"text-danger\">*</span>
                                                            </label>
                                                            <input type=\"number\" class=\"form-control module-price\" name=\"modules[{{ loop.index0 }}][price]\" value=\"{{ module.price }}\" placeholder=\"0.00\" step=\"0.01\" min=\"0\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Module Learning Outcomes -->
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Learning Outcomes <span class=\"text-danger\">*</span>
                                                    </label>
                                                    <div class=\"learning-outcomes-container\">
                                                        {% if module.learningOutcomes and module.learningOutcomes|length > 0 %}
                                                            {% for outcome in module.learningOutcomes %}
                                                            <div class=\"input-group mb-2\">
                                                                <input type=\"text\" class=\"form-control\" name=\"modules[{{ loop.parent.loop.index0 }}][learning_outcomes][]\" value=\"{{ outcome }}\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                                <div class=\"input-group-append\">
                                                                    {% if loop.first %}
                                                                    <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                        <i class=\"fas fa-plus\"></i>
                                                                    </button>
                                                                    {% else %}
                                                                    <button type=\"button\" class=\"btn btn-danger remove-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                                        <i class=\"fas fa-minus\"></i>
                                                                    </button>
                                                                    <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                        <i class=\"fas fa-plus\"></i>
                                                                    </button>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                            {% endfor %}
                                                        {% else %}
                                                        <div class=\"input-group mb-2\">
                                                            <input type=\"text\" class=\"form-control\" name=\"modules[{{ loop.index0 }}][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                            <div class=\"input-group-append\">
                                                                <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                    <i class=\"fas fa-plus\"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        {% endif %}
                                                    </div>
                                                </div>

                                                <!-- Module Features -->
                                                <div class=\"form-group\">
                                                    <label class=\"form-label\">
                                                        <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                        Features <span class=\"text-danger\">*</span>
                                                    </label>
                                                    <div class=\"features-container\">
                                                        {% if module.features and module.features|length > 0 %}
                                                            {% for feature in module.features %}
                                                            <div class=\"input-group mb-2\">
                                                                <input type=\"text\" class=\"form-control\" name=\"modules[{{ loop.parent.loop.index0 }}][features][]\" value=\"{{ feature }}\" placeholder=\"e.g., Interactive trading simulator\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                                <div class=\"input-group-append\">
                                                                    {% if loop.first %}
                                                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                        <i class=\"fas fa-plus\"></i>
                                                                    </button>
                                                                    {% else %}
                                                                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                                                                        <i class=\"fas fa-minus\"></i>
                                                                    </button>
                                                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                        <i class=\"fas fa-plus\"></i>
                                                                    </button>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                            {% endfor %}
                                                        {% else %}
                                                        <div class=\"input-group mb-2\">
                                                            <input type=\"text\" class=\"form-control\" name=\"modules[{{ loop.index0 }}][features][]\" placeholder=\"e.g., Interactive trading simulator\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                            <div class=\"input-group-append\">
                                                                <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                    <i class=\"fas fa-plus\"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        {% endif %}
                                                    </div>
                                                </div>

                                                <!-- Hidden fields -->
                                                <input type=\"hidden\" name=\"modules[{{ loop.index0 }}][is_active]\" value=\"1\">
                                                <input type=\"hidden\" name=\"modules[{{ loop.index0 }}][sort_order]\" value=\"{{ loop.index }}\">
                                            </div>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                    <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                                        <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                                            <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                                                <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                                                Module <span class=\"module-number\">1</span>
                                            </h6>
                                            <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px; padding: 0; display: flex; align-items: center; justify-content: center;\">
                                                <i class=\"fas fa-trash\" style=\"font-size: 0.8rem;\"></i>
                                            </button>
                                        </div>

                                        <div class=\"module-content\">
                                            <!-- Module Code and Title -->
                                            <div class=\"row\">
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Module Code
                                                        </label>
                                                        <input type=\"text\" class=\"form-control module-code\" name=\"modules[0][code]\" placeholder=\"e.g., Module-1\" style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                    </div>
                                                </div>
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Module Title <span class=\"text-danger\">*</span>
                                                        </label>
                                                        <input type=\"text\" class=\"form-control module-title\" name=\"modules[0][title]\" placeholder=\"e.g., Introduction to Trading Fundamentals\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Description -->
                                            <div class=\"form-group\">
                                                <label class=\"form-label\">
                                                    <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                    Module Description <span class=\"text-danger\">*</span>
                                                </label>
                                                <textarea class=\"form-control module-description\" name=\"modules[0][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 100px; border: 2px solid #ced4da; border-radius: 0.375rem;\"></textarea>
                                            </div>

                                            <!-- Module Duration and Price -->
                                            <div class=\"row\">
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Duration (Minutes) <span class=\"text-danger\">*</span>
                                                        </label>
                                                        <input type=\"number\" class=\"form-control module-duration\" name=\"modules[0][duration]\" placeholder=\"e.g., 60\" min=\"1\" max=\"1000\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                    </div>
                                                </div>
                                                <div class=\"col-md-6\">
                                                    <div class=\"form-group\">
                                                        <label class=\"form-label\">
                                                            <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                            Price (USD) <span class=\"text-danger\">*</span>
                                                        </label>
                                                        <input type=\"number\" class=\"form-control module-price\" name=\"modules[0][price]\" placeholder=\"0.00\" step=\"0.01\" min=\"0\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Learning Outcomes -->
                                            <div class=\"form-group\">
                                                <label class=\"form-label\">
                                                    <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                    Learning Outcomes <span class=\"text-danger\">*</span>
                                                </label>
                                                <div class=\"learning-outcomes-container\">
                                                    <div class=\"input-group mb-2\">
                                                        <input type=\"text\" class=\"form-control\" name=\"modules[0][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                        <div class=\"input-group-append\">
                                                            <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                <i class=\"fas fa-plus\"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Features -->
                                            <div class=\"form-group\">
                                                <label class=\"form-label\">
                                                    <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                                    Features <span class=\"text-danger\">*</span>
                                                </label>
                                                <div class=\"features-container\">
                                                    <div class=\"input-group mb-2\">
                                                        <input type=\"text\" class=\"form-control\" name=\"modules[0][features][]\" placeholder=\"e.g., Interactive trading simulator\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                                        <div class=\"input-group-append\">
                                                            <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                                                <i class=\"fas fa-plus\"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Hidden fields -->
                                            <input type=\"hidden\" name=\"modules[0][is_active]\" value=\"1\">
                                            <input type=\"hidden\" name=\"modules[0][sort_order]\" value=\"1\">
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>

                                <button type=\"button\" id=\"add-module\" class=\"btn btn-outline-primary\">
                                    <i class=\"fas fa-plus\" style=\"margin-right: 0.5rem;\"></i>
                                    Add Another Module
                                </button>
                            </div>

                        </div>

                        <!-- Action Buttons Section -->
                        <div class=\"d-flex justify-content-between align-items-center flex-wrap gap-3\">
                                        <button type=\"submit\" class=\"btn btn-lg btn-success\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; transition: all 0.3s ease; min-width: 200px;\">
                                            <i class=\"fas fa-save mr-2\"></i>
                                            Update Course
                                        </button>
                                        <a href=\"{{ path('admin_courses') }}\" class=\"btn btn-lg btn-secondary\" style=\"font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; min-width: 200px; background: #6c757d; border-color: #6c757d;\">
                                            <i class=\"fas fa-times mr-2\"></i>
                                            Cancel
                                        </a>
                        </div>
                    </div>
                </div>

            </div>
        </form>
    </div>
</div>

<!-- Professional Module Removal Modal -->
<div class=\"modal fade\" id=\"courseValidationModal\" tabindex=\"-1\" aria-labelledby=\"courseValidationModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered\">
        <div class=\"modal-content\" style=\"border: none; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #dc3545 100%); color: white; border: none; border-radius: 15px 15px 0 0; padding: 1.5rem;\">
                <h5 class=\"modal-title\" id=\"courseValidationModalLabel\" style=\"font-weight: 600;\">
                    <i class=\"fas fa-exclamation-triangle me-2\"></i>
                    Module Required
                </h5>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                <p class=\"mb-3\" style=\"color: #a90418;\">
                    At least one module is required.
                </p>
                <small class=\"text-muted\">Please add a module or disable the \"Enable Course Modules\" option.</small>
            </div>
            <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                <button type=\"button\" class=\"btn btn-primary btn-sm\" data-bs-dismiss=\"modal\" style=\"transition: all 0.3s ease;\">
                    <i class=\"fas fa-check me-1\"></i>OK
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Function to toggle module field validation based on \"Course Has Modules\" state
    function toggleModuleValidation() {
        var hasModules = \$('#has_modules').is(':checked');
        var moduleInputs = \$('#modules-section input[name*=\"modules\"], #modules-section textarea[name*=\"modules\"]');

        if (hasModules) {
            // Enable validation for module fields
            moduleInputs.each(function() {
                \$(this).attr('required', true);
            });
        } else {
            // Disable validation for module fields
            moduleInputs.each(function() {
                \$(this).removeAttr('required');
                // Clear any validation state
                \$(this).removeClass('is-invalid is-valid');
                \$(this).closest('.form-group').find('.invalid-feedback').hide();
            });
        }
    }

    // Initialize validation state
    toggleModuleValidation();

    // Image preview functionality
    function setupImagePreview(inputId, previewId) {
        \$('#' + inputId).on('change', function(e) {
            const file = e.target.files[0];
            const preview = \$('#' + previewId);
            const img = preview.find('img');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    img.attr('src', e.target.result);
                    preview.show();
                };
                reader.readAsDataURL(file);
            } else {
                preview.hide();
            }
        });
    }

    // Setup image previews
    setupImagePreview('thumbnail_image', 'thumbnail-preview');
    setupImagePreview('banner_image', 'banner-preview');

    // Learning outcomes management
    \$(document).on('click', '.add-outcome', function() {
        var container = \$(this).closest('#learning-outcomes-container, .learning-outcomes-container');
        var isModuleOutcome = container.hasClass('learning-outcomes-container');
        var moduleIndex = isModuleOutcome ? container.closest('.module-item').index() : '';
        var namePrefix = isModuleOutcome ? `modules[\${moduleIndex}][learning_outcomes][]` : 'learning_outcomes[]';

        var outcomeHtml = `
            <div class=\"input-group mb-2\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"\${namePrefix}\" placeholder=\"Enter another learning outcome...\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(outcomeHtml);
        toggleModuleValidation();
    });

    \$(document).on('click', '.remove-outcome', function() {
        \$(this).closest('.input-group').remove();
    });

    // Features management
    \$(document).on('click', '.add-feature', function() {
        var container = \$(this).closest('#features-container, .features-container');
        var isModuleFeature = container.hasClass('features-container');
        var moduleIndex = isModuleFeature ? container.closest('.module-item').index() : '';
        var namePrefix = isModuleFeature ? `modules[\${moduleIndex}][features][]` : 'features[]';

        var featureHtml = `
            <div class=\"input-group mb-2\">
                <input type=\"text\" class=\"form-control enhanced-field\" name=\"\${namePrefix}\" placeholder=\"Enter another feature...\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                <div class=\"input-group-append\">
                    <button type=\"button\" class=\"btn btn-danger remove-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0;\">
                        <i class=\"fas fa-minus\"></i>
                    </button>
                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                        <i class=\"fas fa-plus\"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(featureHtml);
        toggleModuleValidation();
    });

    \$(document).on('click', '.remove-feature', function() {
        \$(this).closest('.input-group').remove();
    });

    // Module management
    var moduleIndex = \$('.module-item').length;

    // Toggle module section visibility
    \$('#has_modules').on('change', function() {
        if (\$(this).is(':checked')) {
            \$('#modules-section').slideDown();
        } else {
            \$('#modules-section').slideUp();
        }
        toggleModuleValidation();
    });

    // Add new module
    \$('#add-module').on('click', function() {
        var newModule = `
            <div class=\"module-item\" style=\"border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\">
                <div class=\"module-header d-flex justify-content-between align-items-center mb-3\">
                    <h6 class=\"mb-0\" style=\"color: #007bff; font-weight: 700;\">
                        <i class=\"fas fa-cube\" style=\"margin-right: 0.5rem;\"></i>
                        Module <span class=\"module-number\">\${moduleIndex + 1}</span>
                    </h6>
                    <button type=\"button\" class=\"btn btn-sm btn-outline-danger remove-module\" style=\"border-radius: 50%; width: 35px; height: 35px; padding: 0; display: flex; align-items: center; justify-content: center;\">
                        <i class=\"fas fa-trash\" style=\"font-size: 0.8rem;\"></i>
                    </button>
                </div>

                <div class=\"module-content\">
                    <!-- Module Code and Title -->
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-hashtag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Module Code
                                </label>
                                <input type=\"text\" class=\"form-control module-code\" name=\"modules[\${moduleIndex}][code]\" placeholder=\"e.g., Module-\${moduleIndex + 1}\" style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-tag\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Module Title <span class=\"text-danger\">*</span>
                                </label>
                                <input type=\"text\" class=\"form-control module-title\" name=\"modules[\${moduleIndex}][title]\" placeholder=\"e.g., Advanced Trading Strategies\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                            </div>
                        </div>
                    </div>

                    <!-- Module Description -->
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-align-left\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                            Module Description <span class=\"text-danger\">*</span>
                        </label>
                        <textarea class=\"form-control module-description\" name=\"modules[\${moduleIndex}][description]\" rows=\"6\" placeholder=\"Detailed description of what this module covers...\" required style=\"min-height: 150px; border: 2px solid #ced4da; border-radius: 0.375rem;\"></textarea>
                    </div>

                    <!-- Module Duration and Price -->
                    <div class=\"row\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-clock\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Duration (Minutes) <span class=\"text-danger\">*</span>
                                </label>
                                <input type=\"number\" class=\"form-control module-duration\" name=\"modules[\${moduleIndex}][duration]\" placeholder=\"e.g., 90\" min=\"1\" max=\"1000\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-dollar-sign\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                                    Price (USD) <span class=\"text-danger\">*</span>
                                </label>
                                <input type=\"number\" class=\"form-control module-price\" name=\"modules[\${moduleIndex}][price]\" placeholder=\"0.00\" step=\"0.01\" min=\"0\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem;\">
                            </div>
                        </div>
                    </div>

                    <!-- Module Learning Outcomes -->
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-graduation-cap\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                            Learning Outcomes <span class=\"text-danger\">*</span>
                        </label>
                        <div class=\"learning-outcomes-container\">
                            <div class=\"input-group mb-2\">
                                <input type=\"text\" class=\"form-control\" name=\"modules[\${moduleIndex}][learning_outcomes][]\" placeholder=\"e.g., Master advanced chart analysis techniques\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                <div class=\"input-group-append\">
                                    <button type=\"button\" class=\"btn btn-success add-outcome\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                        <i class=\"fas fa-plus\"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Module Features -->
                    <div class=\"form-group\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-star\" style=\"color: #007bff; margin-right: 0.5rem;\"></i>
                            Features <span class=\"text-danger\">*</span>
                        </label>
                        <div class=\"features-container\">
                            <div class=\"input-group mb-2\">
                                <input type=\"text\" class=\"form-control\" name=\"modules[\${moduleIndex}][features][]\" placeholder=\"e.g., Interactive trading simulator\" required style=\"height: calc(1.6em + 1.25rem + 4px); border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;\">
                                <div class=\"input-group-append\">
                                    <button type=\"button\" class=\"btn btn-success add-feature\" style=\"height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;\">
                                        <i class=\"fas fa-plus\"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden fields -->
                    <input type=\"hidden\" name=\"modules[\${moduleIndex}][is_active]\" value=\"1\">
                    <input type=\"hidden\" name=\"modules[\${moduleIndex}][sort_order]\" value=\"\${moduleIndex + 1}\">
                </div>
            </div>
        `;

        \$('#modules-container').append(newModule);
        moduleIndex++;
        updateModuleNumbers();
        toggleModuleValidation();
    });

    // Remove module
    \$(document).on('click', '.remove-module', function() {
        \$(this).closest('.module-item').remove();
        updateModuleNumbers();
        toggleModuleValidation();
    });

    // Update module numbers and form field names
    function updateModuleNumbers() {
        \$('.module-item').each(function(index) {
            \$(this).find('.module-number').text(index + 1);
            // Update form field names
            \$(this).find('input, textarea, select').each(function() {
                var name = \$(this).attr('name');
                if (name && name.includes('modules[')) {
                    var newName = name.replace(/modules\\[\\d+\\]/, 'modules[' + index + ']');
                    \$(this).attr('name', newName);
                }
            });
        });
    }

    // Form validation
    \$('#course-form').on('submit', function(e) {
        var hasModules = \$('#has_modules').is(':checked');
        var moduleCount = \$('.module-item').length;

        if (hasModules && moduleCount === 0) {
            e.preventDefault();
            const modal = new bootstrap.Modal(document.getElementById('courseValidationModal'));
            modal.show();
            return false;
        }

        // Update module validation before form submission
        toggleModuleValidation();

        // Validate all required fields
        var isValid = true;
        \$(this).find('input[required], textarea[required], select[required]').each(function() {
            if (!\$(this).val()) {
                isValid = false;
                \$(this).addClass('is-invalid');
            } else {
                \$(this).removeClass('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
});
</script>
{% endblock %}
", "admin/courses/edit.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\courses\\edit.html.twig");
    }
}
