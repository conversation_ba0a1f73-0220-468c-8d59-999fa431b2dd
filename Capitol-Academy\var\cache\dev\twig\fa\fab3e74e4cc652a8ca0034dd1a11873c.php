<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/enrollments/list.html.twig */
class __TwigTemplate_7f0a25e39ae4008236fe3456887ef0ec extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/enrollments/list.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/enrollments/list.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Enrollments Management - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Enrollments Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item active\">Enrollments</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        $context["page_config"] = ["page_title" => "Enrollments Management", "page_icon" => "fas fa-user-graduate", "search_placeholder" => "Search enrollments...", "create_button" => ["url" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_enrollment_create"), "text" => "Manual Enrollment", "icon" => "fas fa-plus"], "stats" => [["title" => "Total Enrollments", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(),         // line 25
(isset($context["enrollments"]) || array_key_exists("enrollments", $context) ? $context["enrollments"] : (function () { throw new RuntimeError('Variable "enrollments" does not exist.', 25, $this->source); })())), "icon" => "fas fa-users", "color" => "#011a2d", "gradient" => "linear-gradient(135deg, #011a2d 0%, #1a3461 100%)"], ["title" => "Active", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 32
(isset($context["enrollments"]) || array_key_exists("enrollments", $context) ? $context["enrollments"] : (function () { throw new RuntimeError('Variable "enrollments" does not exist.', 32, $this->source); })()), function ($__enrollment__) use ($context, $macros) { $context["enrollment"] = $__enrollment__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["enrollment"]) || array_key_exists("enrollment", $context) ? $context["enrollment"] : (function () { throw new RuntimeError('Variable "enrollment" does not exist.', 32, $this->source); })()), "isActive", [], "any", false, false, false, 32); })), "icon" => "fas fa-check-circle", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "Completed", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 39
(isset($context["enrollments"]) || array_key_exists("enrollments", $context) ? $context["enrollments"] : (function () { throw new RuntimeError('Variable "enrollments" does not exist.', 39, $this->source); })()), function ($__enrollment__) use ($context, $macros) { $context["enrollment"] = $__enrollment__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["enrollment"]) || array_key_exists("enrollment", $context) ? $context["enrollment"] : (function () { throw new RuntimeError('Variable "enrollment" does not exist.', 39, $this->source); })()), "isCompleted", [], "any", false, false, false, 39); })), "icon" => "fas fa-graduation-cap", "color" => "#17a2b8", "gradient" => "linear-gradient(135deg, #17a2b8 0%, #138496 100%)"], ["title" => "Recent (30 days)", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 46
(isset($context["enrollments"]) || array_key_exists("enrollments", $context) ? $context["enrollments"] : (function () { throw new RuntimeError('Variable "enrollments" does not exist.', 46, $this->source); })()), function ($__enrollment__) use ($context, $macros) { $context["enrollment"] = $__enrollment__; return (CoreExtension::getAttribute($this->env, $this->source, (isset($context["enrollment"]) || array_key_exists("enrollment", $context) ? $context["enrollment"] : (function () { throw new RuntimeError('Variable "enrollment" does not exist.', 46, $this->source); })()), "enrolledAt", [], "any", false, false, false, 46) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["enrollment"]) || array_key_exists("enrollment", $context) ? $context["enrollment"] : (function () { throw new RuntimeError('Variable "enrollment" does not exist.', 46, $this->source); })()), "enrolledAt", [], "any", false, false, false, 46) > $this->extensions['Twig\Extension\CoreExtension']->convertDate("-30 days"))); })), "icon" => "fas fa-clock", "color" => "#a90418", "gradient" => "linear-gradient(135deg, #a90418 0%, #8b0314 100%)"]]];
        // line 53
        yield "
";
        // line 54
        yield from $this->load("admin/enrollments/list.html.twig", 54, "1697809350")->unwrap()->yield(CoreExtension::merge($context, (isset($context["page_config"]) || array_key_exists("page_config", $context) ? $context["page_config"] : (function () { throw new RuntimeError('Variable "page_config" does not exist.', 54, $this->source); })())));
        // line 163
        yield "
<!-- Enrollment Action Modals -->
<!-- Block Enrollment Modal -->
<div class=\"modal fade\" id=\"blockEnrollmentModal\" tabindex=\"-1\" aria-labelledby=\"blockEnrollmentModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered modal-sm\">
        <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: #212529; border: none; padding: 1rem;\">
                <h6 class=\"modal-title\" id=\"blockEnrollmentModalLabel\" style=\"font-weight: 600;\">
                    <i class=\"fas fa-ban me-2\"></i>Block Enrollment
                </h6>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                <p class=\"mb-3\" style=\"color: #011a2d;\">Are you sure you want to block this enrollment?</p>
                <div id=\"blockEnrollmentDetails\" class=\"text-muted mb-3\"></div>
                <small class=\"text-muted\">This will prevent the student from accessing the course content.</small>
            </div>
            <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                <button type=\"button\" class=\"btn btn-secondary btn-sm\" data-bs-dismiss=\"modal\">
                    <i class=\"fas fa-times me-1\"></i>Cancel
                </button>
                <button type=\"button\" class=\"btn btn-sm\" id=\"confirmBlockBtn\" style=\"background: #ffc107; color: #212529; border: none;\">
                    <i class=\"fas fa-ban me-1\"></i>Block Enrollment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Unblock Enrollment Modal -->
<div class=\"modal fade\" id=\"unblockEnrollmentModal\" tabindex=\"-1\" aria-labelledby=\"unblockEnrollmentModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered modal-sm\">
        <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 1rem;\">
                <h6 class=\"modal-title\" id=\"unblockEnrollmentModalLabel\" style=\"font-weight: 600;\">
                    <i class=\"fas fa-check me-2\"></i>Unblock Enrollment
                </h6>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                <p class=\"mb-3\" style=\"color: #011a2d;\">Are you sure you want to unblock this enrollment?</p>
                <div id=\"unblockEnrollmentDetails\" class=\"text-muted mb-3\"></div>
                <small class=\"text-muted\">This will restore the student's access to the course content.</small>
            </div>
            <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                <button type=\"button\" class=\"btn btn-secondary btn-sm\" data-bs-dismiss=\"modal\">
                    <i class=\"fas fa-times me-1\"></i>Cancel
                </button>
                <button type=\"button\" class=\"btn btn-sm\" id=\"confirmUnblockBtn\" style=\"background: #28a745; color: white; border: none;\">
                    <i class=\"fas fa-check me-1\"></i>Unblock Enrollment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Enrollment Modal -->
<div class=\"modal fade\" id=\"deleteEnrollmentModal\" tabindex=\"-1\" aria-labelledby=\"deleteEnrollmentModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered modal-sm\">
        <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;\">
                <h6 class=\"modal-title\" id=\"deleteEnrollmentModalLabel\" style=\"font-weight: 600;\">
                    <i class=\"fas fa-trash me-2\"></i>Delete Enrollment
                </h6>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                <p class=\"mb-3\" style=\"color: #011a2d;\">Are you sure you want to delete this enrollment?</p>
                <div id=\"deleteEnrollmentDetails\" class=\"text-muted mb-3\"></div>
                <small class=\"text-danger\"><strong>Warning:</strong> This action cannot be undone.</small>
            </div>
            <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                <button type=\"button\" class=\"btn btn-secondary btn-sm\" data-bs-dismiss=\"modal\">
                    <i class=\"fas fa-times me-1\"></i>Cancel
                </button>
                <button type=\"button\" class=\"btn btn-sm\" id=\"confirmDeleteBtn\" style=\"background: #a90418; color: white; border: none;\">
                    <i class=\"fas fa-trash me-1\"></i>Delete Enrollment
                </button>
            </div>
        </div>
    </div>
</div>

";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 248
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 249
        yield "<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.enrollment-row',
        ['.enrollment-student-name', '.enrollment-course-code', '.enrollment-course-title']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Modal functions for enrollment actions
function showBlockModal(enrollmentId, studentName, courseCode) {
    document.getElementById('blockEnrollmentDetails').innerHTML = `
        <strong>\${studentName}</strong><br>
        <small>Course: \${courseCode}</small>
    `;

    const modal = new bootstrap.Modal(document.getElementById('blockEnrollmentModal'));
    const confirmBtn = document.getElementById('confirmBlockBtn');

    // Remove existing event listeners
    confirmBtn.replaceWith(confirmBtn.cloneNode(true));
    const newConfirmBtn = document.getElementById('confirmBlockBtn');

    newConfirmBtn.addEventListener('click', function() {
        modal.hide();
        blockEnrollment(enrollmentId);
    });

    modal.show();
}

function blockEnrollment(enrollmentId) {
    fetch(`";
        // line 288
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_enrollment_block", ["id" => "0"]);
        yield "`.replace('0', enrollmentId), {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to block enrollment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while blocking the enrollment');
    });
}

function showUnblockModal(enrollmentId, studentName, courseCode) {
    document.getElementById('unblockEnrollmentDetails').innerHTML = `
        <strong>\${studentName}</strong><br>
        <small>Course: \${courseCode}</small>
    `;

    const modal = new bootstrap.Modal(document.getElementById('unblockEnrollmentModal'));
    const confirmBtn = document.getElementById('confirmUnblockBtn');

    // Remove existing event listeners
    confirmBtn.replaceWith(confirmBtn.cloneNode(true));
    const newConfirmBtn = document.getElementById('confirmUnblockBtn');

    newConfirmBtn.addEventListener('click', function() {
        modal.hide();
        unblockEnrollment(enrollmentId);
    });

    modal.show();
}

function unblockEnrollment(enrollmentId) {
    fetch(`";
        // line 330
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_enrollment_unblock", ["id" => "0"]);
        yield "`.replace('0', enrollmentId), {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to unblock enrollment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while unblocking the enrollment');
    });
}

function showDeleteModal(enrollmentId, studentName, courseCode) {
    document.getElementById('deleteEnrollmentDetails').innerHTML = `
        <strong>\${studentName}</strong><br>
        <small>Course: \${courseCode}</small>
    `;

    const modal = new bootstrap.Modal(document.getElementById('deleteEnrollmentModal'));
    const confirmBtn = document.getElementById('confirmDeleteBtn');

    // Remove existing event listeners
    confirmBtn.replaceWith(confirmBtn.cloneNode(true));
    const newConfirmBtn = document.getElementById('confirmDeleteBtn');

    newConfirmBtn.addEventListener('click', function() {
        modal.hide();
        deleteEnrollment(enrollmentId);
    });

    modal.show();
}

function deleteEnrollment(enrollmentId) {
    fetch(`";
        // line 372
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_enrollment_delete", ["id" => "0"]);
        yield "`.replace('0', enrollmentId), {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to delete enrollment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the enrollment');
    });
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/enrollments/list.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  403 => 372,  358 => 330,  313 => 288,  272 => 249,  259 => 248,  165 => 163,  163 => 54,  160 => 53,  158 => 46,  157 => 39,  156 => 32,  155 => 25,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Enrollments Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Enrollments Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Enrollments</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Enrollments Management',
    'page_icon': 'fas fa-user-graduate',
    'search_placeholder': 'Search enrollments...',
    'create_button': {
        'url': path('admin_enrollment_create'),
        'text': 'Manual Enrollment',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Enrollments',
            'value': enrollments|length,
            'icon': 'fas fa-users',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': enrollments|filter(enrollment => enrollment.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Completed',
            'value': enrollments|filter(enrollment => enrollment.isCompleted)|length,
            'icon': 'fas fa-graduation-cap',
            'color': '#17a2b8',
            'gradient': 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': enrollments|filter(enrollment => enrollment.enrolledAt and enrollment.enrolledAt > date('-30 days'))|length,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Student'},
            {'text': 'Course'},
            {'text': 'Enrolled Date'},
            {'text': 'Status'},
            {'text': 'Progress'},
            {'text': 'Payment'},
            {'text': 'Actions', 'style': 'width: 150px;'}
        ] %}

        {% set table_rows = [] %}
        {% for enrollment in enrollments %}
            {% set row_cells = [
                {
                    'content': '<div class=\"d-flex align-items-center\">
                        <div class=\"enrollment-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mr-2\" style=\"width: 40px; height: 40px; font-size: 14px; font-weight: 600;\">
                            ' ~ enrollment.user.firstName|first|upper ~ enrollment.user.lastName|first|upper ~ '
                        </div>
                        <div>
                            <div>
                                <a href=\"' ~ path('admin_user_details', {'emailPrefix': enrollment.user.emailPrefix}) ~ '\" class=\"enrollment-student-name font-weight-bold text-primary text-decoration-none\" style=\"color: #011a2d !important;\">
                                    ' ~ enrollment.user.firstName ~ ' ' ~ enrollment.user.lastName ~ '
                                </a>
                            </div>
                        </div>
                    </div>'
                },
                {
                    'content': '<div>
                        <div>
                            <a href=\"' ~ path('admin_course_preview', {'code': enrollment.course.code}) ~ '\" class=\"text-decoration-none\" style=\"color: #011a2d;\">
                                <code class=\"enrollment-course-code bg-light text-dark\" style=\"padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 600; margin-right: 0.5rem;\">' ~ enrollment.course.code ~ '</code>
                            </a>
                        </div>
                    </div>'
                },
                {
                    'content': '<div>
                        <span class=\"text-dark font-weight-medium\">' ~ enrollment.enrolledAt|date('M j, Y') ~ '</span>
                    </div>'
                },
                {
                    'content': (enrollment.isCompleted ?
                        '<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Completed</span>
                        <br>' :
                        (enrollment.isActive ?
                            '<span class=\"badge\" style=\"background: #007bff; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-play-circle mr-1\"></i> Active</span>' :
                            '<span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-pause-circle mr-1\"></i> Inactive</span>'
                        )
                    )
                },
                {
                    'content': '<div class=\"progress\" style=\"height: 20px; background:rgb(179, 202, 225); border-radius: 10px;\">
                        <div class=\"progress-bar\" role=\"progressbar\"
                             style=\"width: ' ~ enrollment.progressPercentage ~ '%; background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); border-radius: 10px;\"
                             aria-valuenow=\"' ~ enrollment.progressPercentage ~ '\"
                             aria-valuemin=\"0\" aria-valuemax=\"100\">
                        </div>
                    </div>'
                },
                {
                    'content': (enrollment.payment ?
                        (enrollment.payment.isSuccessful ?
                            '<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-credit-card mr-1\"></i> Paid</span>
                            <br>' :
                            '<span class=\"badge\" style=\"background: #ffc107; color: #212529; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-clock mr-1\"></i> ' ~ enrollment.payment.status|title ~ '</span>'
                        ) :
                        '<span class=\"badge\" style=\"background: #17a2b8; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-gift mr-1\"></i> Free</span>'
                    )
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_enrollment_details_by_code', {'courseCode': enrollment.course.code, 'studentName': enrollment.user.fullName|replace({' ': '-'})|lower}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Details\"><i class=\"fas fa-eye\"></i></a>
                        ' ~ (enrollment.isCertified ?
                            '<span class=\"btn btn-sm shadow-sm\" style=\"background: #6c757d; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px; cursor: not-allowed; opacity: 0.6;\" title=\"Certified enrollments cannot be edited\"><i class=\"fas fa-edit\"></i></span>' :
                            '<a href=\"' ~ path('admin_enrollment_edit', {'courseCode': enrollment.course.code, 'studentName': enrollment.user.fullName|replace({' ': '-'})|lower}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Enrollment\"><i class=\"fas fa-edit\"></i></a>'
                        ) ~ '
                        ' ~ (enrollment.isCertified ? '' :
                            (enrollment.status != 'blocked' ?
                                '<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #ffc107; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Block Enrollment\" onclick=\"showBlockModal(' ~ enrollment.id ~ ', \\'' ~ enrollment.user.firstName ~ ' ' ~ enrollment.user.lastName ~ '\\', \\'' ~ enrollment.course.code ~ '\\')\"><i class=\"fas fa-ban\"></i></button>' :
                                '<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #28a745; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Unblock Enrollment\" onclick=\"showUnblockModal(' ~ enrollment.id ~ ', \\'' ~ enrollment.user.firstName ~ ' ' ~ enrollment.user.lastName ~ '\\', \\'' ~ enrollment.course.code ~ '\\')\"><i class=\"fas fa-check\"></i></button>'
                            )
                        ) ~ '
                        ' ~ (enrollment.isCertified ?
                            '<span class=\"btn btn-sm shadow-sm\" style=\"background: #6c757d; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; cursor: not-allowed; opacity: 0.6;\" title=\"Certified enrollments cannot be deleted\"><i class=\"fas fa-trash\"></i></span>' :
                            '<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Enrollment\" onclick=\"showDeleteModal(' ~ enrollment.id ~ ', \\'' ~ enrollment.user.firstName ~ ' ' ~ enrollment.user.lastName ~ '\\', \\'' ~ enrollment.course.code ~ '\\')\"><i class=\"fas fa-trash\"></i></button>'
                        ) ~ '
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'enrollment-row',
            'empty_message': 'No enrollments found',
            'empty_icon': 'fas fa-user-graduate',
            'empty_description': 'Try adjusting your search or create a new enrollment.',
            'search_config': {
                'fields': ['.enrollment-student-name', '.enrollment-course-code', '.enrollment-course-title']
            }
        } %}
    {% endblock %}
{% endembed %}

<!-- Enrollment Action Modals -->
<!-- Block Enrollment Modal -->
<div class=\"modal fade\" id=\"blockEnrollmentModal\" tabindex=\"-1\" aria-labelledby=\"blockEnrollmentModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered modal-sm\">
        <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: #212529; border: none; padding: 1rem;\">
                <h6 class=\"modal-title\" id=\"blockEnrollmentModalLabel\" style=\"font-weight: 600;\">
                    <i class=\"fas fa-ban me-2\"></i>Block Enrollment
                </h6>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                <p class=\"mb-3\" style=\"color: #011a2d;\">Are you sure you want to block this enrollment?</p>
                <div id=\"blockEnrollmentDetails\" class=\"text-muted mb-3\"></div>
                <small class=\"text-muted\">This will prevent the student from accessing the course content.</small>
            </div>
            <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                <button type=\"button\" class=\"btn btn-secondary btn-sm\" data-bs-dismiss=\"modal\">
                    <i class=\"fas fa-times me-1\"></i>Cancel
                </button>
                <button type=\"button\" class=\"btn btn-sm\" id=\"confirmBlockBtn\" style=\"background: #ffc107; color: #212529; border: none;\">
                    <i class=\"fas fa-ban me-1\"></i>Block Enrollment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Unblock Enrollment Modal -->
<div class=\"modal fade\" id=\"unblockEnrollmentModal\" tabindex=\"-1\" aria-labelledby=\"unblockEnrollmentModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered modal-sm\">
        <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 1rem;\">
                <h6 class=\"modal-title\" id=\"unblockEnrollmentModalLabel\" style=\"font-weight: 600;\">
                    <i class=\"fas fa-check me-2\"></i>Unblock Enrollment
                </h6>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                <p class=\"mb-3\" style=\"color: #011a2d;\">Are you sure you want to unblock this enrollment?</p>
                <div id=\"unblockEnrollmentDetails\" class=\"text-muted mb-3\"></div>
                <small class=\"text-muted\">This will restore the student's access to the course content.</small>
            </div>
            <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                <button type=\"button\" class=\"btn btn-secondary btn-sm\" data-bs-dismiss=\"modal\">
                    <i class=\"fas fa-times me-1\"></i>Cancel
                </button>
                <button type=\"button\" class=\"btn btn-sm\" id=\"confirmUnblockBtn\" style=\"background: #28a745; color: white; border: none;\">
                    <i class=\"fas fa-check me-1\"></i>Unblock Enrollment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Enrollment Modal -->
<div class=\"modal fade\" id=\"deleteEnrollmentModal\" tabindex=\"-1\" aria-labelledby=\"deleteEnrollmentModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered modal-sm\">
        <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;\">
                <h6 class=\"modal-title\" id=\"deleteEnrollmentModalLabel\" style=\"font-weight: 600;\">
                    <i class=\"fas fa-trash me-2\"></i>Delete Enrollment
                </h6>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                <p class=\"mb-3\" style=\"color: #011a2d;\">Are you sure you want to delete this enrollment?</p>
                <div id=\"deleteEnrollmentDetails\" class=\"text-muted mb-3\"></div>
                <small class=\"text-danger\"><strong>Warning:</strong> This action cannot be undone.</small>
            </div>
            <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                <button type=\"button\" class=\"btn btn-secondary btn-sm\" data-bs-dismiss=\"modal\">
                    <i class=\"fas fa-times me-1\"></i>Cancel
                </button>
                <button type=\"button\" class=\"btn btn-sm\" id=\"confirmDeleteBtn\" style=\"background: #a90418; color: white; border: none;\">
                    <i class=\"fas fa-trash me-1\"></i>Delete Enrollment
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.enrollment-row',
        ['.enrollment-student-name', '.enrollment-course-code', '.enrollment-course-title']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Modal functions for enrollment actions
function showBlockModal(enrollmentId, studentName, courseCode) {
    document.getElementById('blockEnrollmentDetails').innerHTML = `
        <strong>\${studentName}</strong><br>
        <small>Course: \${courseCode}</small>
    `;

    const modal = new bootstrap.Modal(document.getElementById('blockEnrollmentModal'));
    const confirmBtn = document.getElementById('confirmBlockBtn');

    // Remove existing event listeners
    confirmBtn.replaceWith(confirmBtn.cloneNode(true));
    const newConfirmBtn = document.getElementById('confirmBlockBtn');

    newConfirmBtn.addEventListener('click', function() {
        modal.hide();
        blockEnrollment(enrollmentId);
    });

    modal.show();
}

function blockEnrollment(enrollmentId) {
    fetch(`{{ path('admin_enrollment_block', {'id': '0'}) }}`.replace('0', enrollmentId), {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to block enrollment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while blocking the enrollment');
    });
}

function showUnblockModal(enrollmentId, studentName, courseCode) {
    document.getElementById('unblockEnrollmentDetails').innerHTML = `
        <strong>\${studentName}</strong><br>
        <small>Course: \${courseCode}</small>
    `;

    const modal = new bootstrap.Modal(document.getElementById('unblockEnrollmentModal'));
    const confirmBtn = document.getElementById('confirmUnblockBtn');

    // Remove existing event listeners
    confirmBtn.replaceWith(confirmBtn.cloneNode(true));
    const newConfirmBtn = document.getElementById('confirmUnblockBtn');

    newConfirmBtn.addEventListener('click', function() {
        modal.hide();
        unblockEnrollment(enrollmentId);
    });

    modal.show();
}

function unblockEnrollment(enrollmentId) {
    fetch(`{{ path('admin_enrollment_unblock', {'id': '0'}) }}`.replace('0', enrollmentId), {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to unblock enrollment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while unblocking the enrollment');
    });
}

function showDeleteModal(enrollmentId, studentName, courseCode) {
    document.getElementById('deleteEnrollmentDetails').innerHTML = `
        <strong>\${studentName}</strong><br>
        <small>Course: \${courseCode}</small>
    `;

    const modal = new bootstrap.Modal(document.getElementById('deleteEnrollmentModal'));
    const confirmBtn = document.getElementById('confirmDeleteBtn');

    // Remove existing event listeners
    confirmBtn.replaceWith(confirmBtn.cloneNode(true));
    const newConfirmBtn = document.getElementById('confirmDeleteBtn');

    newConfirmBtn.addEventListener('click', function() {
        modal.hide();
        deleteEnrollment(enrollmentId);
    });

    modal.show();
}

function deleteEnrollment(enrollmentId) {
    fetch(`{{ path('admin_enrollment_delete', {'id': '0'}) }}`.replace('0', enrollmentId), {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to delete enrollment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the enrollment');
    });
}
</script>
{% endblock %}", "admin/enrollments/list.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\enrollments\\list.html.twig");
    }
}


/* admin/enrollments/list.html.twig */
class __TwigTemplate_7f0a25e39ae4008236fe3456887ef0ec___1697809350 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 54
        return "components/admin_page_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/enrollments/list.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/enrollments/list.html.twig"));

        $this->parent = $this->load("components/admin_page_layout.html.twig", 54);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 55
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 56
        yield "        <!-- Standardized Table -->
        ";
        // line 57
        $context["table_headers"] = [["text" => "Student"], ["text" => "Course"], ["text" => "Enrolled Date"], ["text" => "Status"], ["text" => "Progress"], ["text" => "Payment"], ["text" => "Actions", "style" => "width: 150px;"]];
        // line 66
        yield "
        ";
        // line 67
        $context["table_rows"] = [];
        // line 68
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["enrollments"]) || array_key_exists("enrollments", $context) ? $context["enrollments"] : (function () { throw new RuntimeError('Variable "enrollments" does not exist.', 68, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["enrollment"]) {
            // line 69
            yield "            ";
            $context["row_cells"] = [["content" => ((((((((("<div class=\"d-flex align-items-center\">
                        <div class=\"enrollment-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mr-2\" style=\"width: 40px; height: 40px; font-size: 14px; font-weight: 600;\">
                            " . Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,             // line 73
$context["enrollment"], "user", [], "any", false, false, false, 73), "firstName", [], "any", false, false, false, 73)))) . Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["enrollment"], "user", [], "any", false, false, false, 73), "lastName", [], "any", false, false, false, 73)))) . "
                        </div>
                        <div>
                            <div>
                                <a href=\"") . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_user_details", ["emailPrefix" => CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,             // line 77
$context["enrollment"], "user", [], "any", false, false, false, 77), "emailPrefix", [], "any", false, false, false, 77)])) . "\" class=\"enrollment-student-name font-weight-bold text-primary text-decoration-none\" style=\"color: #011a2d !important;\">
                                    ") . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,             // line 78
$context["enrollment"], "user", [], "any", false, false, false, 78), "firstName", [], "any", false, false, false, 78)) . " ") . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["enrollment"], "user", [], "any", false, false, false, 78), "lastName", [], "any", false, false, false, 78)) . "
                                </a>
                            </div>
                        </div>
                    </div>")], ["content" => (((("<div>
                        <div>
                            <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_course_preview", ["code" => CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,             // line 87
$context["enrollment"], "course", [], "any", false, false, false, 87), "code", [], "any", false, false, false, 87)])) . "\" class=\"text-decoration-none\" style=\"color: #011a2d;\">
                                <code class=\"enrollment-course-code bg-light text-dark\" style=\"padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 600; margin-right: 0.5rem;\">") . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,             // line 88
$context["enrollment"], "course", [], "any", false, false, false, 88), "code", [], "any", false, false, false, 88)) . "</code>
                            </a>
                        </div>
                    </div>")], ["content" => (("<div>
                        <span class=\"text-dark font-weight-medium\">" . $this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source,             // line 95
$context["enrollment"], "enrolledAt", [], "any", false, false, false, 95), "M j, Y")) . "</span>
                    </div>")], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 99
$context["enrollment"], "isCompleted", [], "any", false, false, false, 99)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Completed</span>
                        <br>") : ((((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 102
$context["enrollment"], "isActive", [], "any", false, false, false, 102)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge\" style=\"background: #007bff; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-play-circle mr-1\"></i> Active</span>") : ("<span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-pause-circle mr-1\"></i> Inactive</span>"))))], ["content" => (((("<div class=\"progress\" style=\"height: 20px; background:rgb(179, 202, 225); border-radius: 10px;\">
                        <div class=\"progress-bar\" role=\"progressbar\"
                             style=\"width: " . CoreExtension::getAttribute($this->env, $this->source,             // line 111
$context["enrollment"], "progressPercentage", [], "any", false, false, false, 111)) . "%; background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); border-radius: 10px;\"
                             aria-valuenow=\"") . CoreExtension::getAttribute($this->env, $this->source,             // line 112
$context["enrollment"], "progressPercentage", [], "any", false, false, false, 112)) . "\"
                             aria-valuemin=\"0\" aria-valuemax=\"100\">
                        </div>
                    </div>")], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 118
$context["enrollment"], "payment", [], "any", false, false, false, 118)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((((($tmp = CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,             // line 119
$context["enrollment"], "payment", [], "any", false, false, false, 119), "isSuccessful", [], "any", false, false, false, 119)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-credit-card mr-1\"></i> Paid</span>
                            <br>") : ((("<span class=\"badge\" style=\"background: #ffc107; color: #212529; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-clock mr-1\"></i> " . Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,             // line 122
$context["enrollment"], "payment", [], "any", false, false, false, 122), "status", [], "any", false, false, false, 122))) . "</span>")))) : ("<span class=\"badge\" style=\"background: #17a2b8; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-gift mr-1\"></i> Free</span>"))], ["content" => (((((((("<div class=\"btn-group\" role=\"group\">
                        <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_enrollment_details_by_code", ["courseCode" => CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,             // line 129
$context["enrollment"], "course", [], "any", false, false, false, 129), "code", [], "any", false, false, false, 129), "studentName" => Twig\Extension\CoreExtension::lower($this->env->getCharset(), Twig\Extension\CoreExtension::replace(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["enrollment"], "user", [], "any", false, false, false, 129), "fullName", [], "any", false, false, false, 129), [" " => "-"]))])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Details\"><i class=\"fas fa-eye\"></i></a>
                        ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 130
$context["enrollment"], "isCertified", [], "any", false, false, false, 130)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"btn btn-sm shadow-sm\" style=\"background: #6c757d; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px; cursor: not-allowed; opacity: 0.6;\" title=\"Certified enrollments cannot be edited\"><i class=\"fas fa-edit\"></i></span>") : ((("<a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_enrollment_edit", ["courseCode" => CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,             // line 132
$context["enrollment"], "course", [], "any", false, false, false, 132), "code", [], "any", false, false, false, 132), "studentName" => Twig\Extension\CoreExtension::lower($this->env->getCharset(), Twig\Extension\CoreExtension::replace(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["enrollment"], "user", [], "any", false, false, false, 132), "fullName", [], "any", false, false, false, 132), [" " => "-"]))])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Enrollment\"><i class=\"fas fa-edit\"></i></a>")))) . "
                        ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 134
$context["enrollment"], "isCertified", [], "any", false, false, false, 134)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("") : ((((CoreExtension::getAttribute($this->env, $this->source,             // line 135
$context["enrollment"], "status", [], "any", false, false, false, 135) != "blocked")) ? ((((((((("<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #ffc107; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Block Enrollment\" onclick=\"showBlockModal(" . CoreExtension::getAttribute($this->env, $this->source,             // line 136
$context["enrollment"], "id", [], "any", false, false, false, 136)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["enrollment"], "user", [], "any", false, false, false, 136), "firstName", [], "any", false, false, false, 136)) . " ") . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["enrollment"], "user", [], "any", false, false, false, 136), "lastName", [], "any", false, false, false, 136)) . "', '") . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["enrollment"], "course", [], "any", false, false, false, 136), "code", [], "any", false, false, false, 136)) . "')\"><i class=\"fas fa-ban\"></i></button>")) : ((((((((("<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #28a745; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Unblock Enrollment\" onclick=\"showUnblockModal(" . CoreExtension::getAttribute($this->env, $this->source,             // line 137
$context["enrollment"], "id", [], "any", false, false, false, 137)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["enrollment"], "user", [], "any", false, false, false, 137), "firstName", [], "any", false, false, false, 137)) . " ") . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["enrollment"], "user", [], "any", false, false, false, 137), "lastName", [], "any", false, false, false, 137)) . "', '") . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["enrollment"], "course", [], "any", false, false, false, 137), "code", [], "any", false, false, false, 137)) . "')\"><i class=\"fas fa-check\"></i></button>")))))) . "
                        ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 140
$context["enrollment"], "isCertified", [], "any", false, false, false, 140)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"btn btn-sm shadow-sm\" style=\"background: #6c757d; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; cursor: not-allowed; opacity: 0.6;\" title=\"Certified enrollments cannot be deleted\"><i class=\"fas fa-trash\"></i></span>") : ((((((((("<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Enrollment\" onclick=\"showDeleteModal(" . CoreExtension::getAttribute($this->env, $this->source,             // line 142
$context["enrollment"], "id", [], "any", false, false, false, 142)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["enrollment"], "user", [], "any", false, false, false, 142), "firstName", [], "any", false, false, false, 142)) . " ") . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["enrollment"], "user", [], "any", false, false, false, 142), "lastName", [], "any", false, false, false, 142)) . "', '") . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["enrollment"], "course", [], "any", false, false, false, 142), "code", [], "any", false, false, false, 142)) . "')\"><i class=\"fas fa-trash\"></i></button>")))) . "
                    </div>")]];
            // line 147
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 147, $this->source); })()), [["cells" => (isset($context["row_cells"]) || array_key_exists("row_cells", $context) ? $context["row_cells"] : (function () { throw new RuntimeError('Variable "row_cells" does not exist.', 147, $this->source); })())]]);
            // line 148
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['enrollment'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 149
        yield "
        ";
        // line 150
        yield from $this->load("components/admin_table.html.twig", 150)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 151
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 151, $this->source); })()), "rows" =>         // line 152
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 152, $this->source); })()), "row_class" => "enrollment-row", "empty_message" => "No enrollments found", "empty_icon" => "fas fa-user-graduate", "empty_description" => "Try adjusting your search or create a new enrollment.", "search_config" => ["fields" => [".enrollment-student-name", ".enrollment-course-code", ".enrollment-course-title"]]]));
        // line 161
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/enrollments/list.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  1003 => 161,  1001 => 152,  1000 => 151,  999 => 150,  996 => 149,  990 => 148,  987 => 147,  984 => 142,  983 => 140,  981 => 137,  980 => 136,  979 => 135,  978 => 134,  976 => 132,  975 => 130,  973 => 129,  971 => 122,  969 => 119,  968 => 118,  964 => 112,  962 => 111,  959 => 102,  957 => 99,  955 => 95,  950 => 88,  948 => 87,  941 => 78,  939 => 77,  934 => 73,  930 => 69,  925 => 68,  923 => 67,  920 => 66,  918 => 57,  915 => 56,  902 => 55,  879 => 54,  403 => 372,  358 => 330,  313 => 288,  272 => 249,  259 => 248,  165 => 163,  163 => 54,  160 => 53,  158 => 46,  157 => 39,  156 => 32,  155 => 25,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Enrollments Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Enrollments Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Enrollments</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Enrollments Management',
    'page_icon': 'fas fa-user-graduate',
    'search_placeholder': 'Search enrollments...',
    'create_button': {
        'url': path('admin_enrollment_create'),
        'text': 'Manual Enrollment',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Enrollments',
            'value': enrollments|length,
            'icon': 'fas fa-users',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': enrollments|filter(enrollment => enrollment.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Completed',
            'value': enrollments|filter(enrollment => enrollment.isCompleted)|length,
            'icon': 'fas fa-graduation-cap',
            'color': '#17a2b8',
            'gradient': 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': enrollments|filter(enrollment => enrollment.enrolledAt and enrollment.enrolledAt > date('-30 days'))|length,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Student'},
            {'text': 'Course'},
            {'text': 'Enrolled Date'},
            {'text': 'Status'},
            {'text': 'Progress'},
            {'text': 'Payment'},
            {'text': 'Actions', 'style': 'width: 150px;'}
        ] %}

        {% set table_rows = [] %}
        {% for enrollment in enrollments %}
            {% set row_cells = [
                {
                    'content': '<div class=\"d-flex align-items-center\">
                        <div class=\"enrollment-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mr-2\" style=\"width: 40px; height: 40px; font-size: 14px; font-weight: 600;\">
                            ' ~ enrollment.user.firstName|first|upper ~ enrollment.user.lastName|first|upper ~ '
                        </div>
                        <div>
                            <div>
                                <a href=\"' ~ path('admin_user_details', {'emailPrefix': enrollment.user.emailPrefix}) ~ '\" class=\"enrollment-student-name font-weight-bold text-primary text-decoration-none\" style=\"color: #011a2d !important;\">
                                    ' ~ enrollment.user.firstName ~ ' ' ~ enrollment.user.lastName ~ '
                                </a>
                            </div>
                        </div>
                    </div>'
                },
                {
                    'content': '<div>
                        <div>
                            <a href=\"' ~ path('admin_course_preview', {'code': enrollment.course.code}) ~ '\" class=\"text-decoration-none\" style=\"color: #011a2d;\">
                                <code class=\"enrollment-course-code bg-light text-dark\" style=\"padding: 0.25rem 0.5rem; border-radius: 4px; font-weight: 600; margin-right: 0.5rem;\">' ~ enrollment.course.code ~ '</code>
                            </a>
                        </div>
                    </div>'
                },
                {
                    'content': '<div>
                        <span class=\"text-dark font-weight-medium\">' ~ enrollment.enrolledAt|date('M j, Y') ~ '</span>
                    </div>'
                },
                {
                    'content': (enrollment.isCompleted ?
                        '<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Completed</span>
                        <br>' :
                        (enrollment.isActive ?
                            '<span class=\"badge\" style=\"background: #007bff; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-play-circle mr-1\"></i> Active</span>' :
                            '<span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-pause-circle mr-1\"></i> Inactive</span>'
                        )
                    )
                },
                {
                    'content': '<div class=\"progress\" style=\"height: 20px; background:rgb(179, 202, 225); border-radius: 10px;\">
                        <div class=\"progress-bar\" role=\"progressbar\"
                             style=\"width: ' ~ enrollment.progressPercentage ~ '%; background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); border-radius: 10px;\"
                             aria-valuenow=\"' ~ enrollment.progressPercentage ~ '\"
                             aria-valuemin=\"0\" aria-valuemax=\"100\">
                        </div>
                    </div>'
                },
                {
                    'content': (enrollment.payment ?
                        (enrollment.payment.isSuccessful ?
                            '<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-credit-card mr-1\"></i> Paid</span>
                            <br>' :
                            '<span class=\"badge\" style=\"background: #ffc107; color: #212529; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-clock mr-1\"></i> ' ~ enrollment.payment.status|title ~ '</span>'
                        ) :
                        '<span class=\"badge\" style=\"background: #17a2b8; color: white; padding: 0.4rem 0.6rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-gift mr-1\"></i> Free</span>'
                    )
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_enrollment_details_by_code', {'courseCode': enrollment.course.code, 'studentName': enrollment.user.fullName|replace({' ': '-'})|lower}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Details\"><i class=\"fas fa-eye\"></i></a>
                        ' ~ (enrollment.isCertified ?
                            '<span class=\"btn btn-sm shadow-sm\" style=\"background: #6c757d; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px; cursor: not-allowed; opacity: 0.6;\" title=\"Certified enrollments cannot be edited\"><i class=\"fas fa-edit\"></i></span>' :
                            '<a href=\"' ~ path('admin_enrollment_edit', {'courseCode': enrollment.course.code, 'studentName': enrollment.user.fullName|replace({' ': '-'})|lower}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Enrollment\"><i class=\"fas fa-edit\"></i></a>'
                        ) ~ '
                        ' ~ (enrollment.isCertified ? '' :
                            (enrollment.status != 'blocked' ?
                                '<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #ffc107; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Block Enrollment\" onclick=\"showBlockModal(' ~ enrollment.id ~ ', \\'' ~ enrollment.user.firstName ~ ' ' ~ enrollment.user.lastName ~ '\\', \\'' ~ enrollment.course.code ~ '\\')\"><i class=\"fas fa-ban\"></i></button>' :
                                '<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #28a745; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Unblock Enrollment\" onclick=\"showUnblockModal(' ~ enrollment.id ~ ', \\'' ~ enrollment.user.firstName ~ ' ' ~ enrollment.user.lastName ~ '\\', \\'' ~ enrollment.course.code ~ '\\')\"><i class=\"fas fa-check\"></i></button>'
                            )
                        ) ~ '
                        ' ~ (enrollment.isCertified ?
                            '<span class=\"btn btn-sm shadow-sm\" style=\"background: #6c757d; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; cursor: not-allowed; opacity: 0.6;\" title=\"Certified enrollments cannot be deleted\"><i class=\"fas fa-trash\"></i></span>' :
                            '<button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Enrollment\" onclick=\"showDeleteModal(' ~ enrollment.id ~ ', \\'' ~ enrollment.user.firstName ~ ' ' ~ enrollment.user.lastName ~ '\\', \\'' ~ enrollment.course.code ~ '\\')\"><i class=\"fas fa-trash\"></i></button>'
                        ) ~ '
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'enrollment-row',
            'empty_message': 'No enrollments found',
            'empty_icon': 'fas fa-user-graduate',
            'empty_description': 'Try adjusting your search or create a new enrollment.',
            'search_config': {
                'fields': ['.enrollment-student-name', '.enrollment-course-code', '.enrollment-course-title']
            }
        } %}
    {% endblock %}
{% endembed %}

<!-- Enrollment Action Modals -->
<!-- Block Enrollment Modal -->
<div class=\"modal fade\" id=\"blockEnrollmentModal\" tabindex=\"-1\" aria-labelledby=\"blockEnrollmentModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered modal-sm\">
        <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: #212529; border: none; padding: 1rem;\">
                <h6 class=\"modal-title\" id=\"blockEnrollmentModalLabel\" style=\"font-weight: 600;\">
                    <i class=\"fas fa-ban me-2\"></i>Block Enrollment
                </h6>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                <p class=\"mb-3\" style=\"color: #011a2d;\">Are you sure you want to block this enrollment?</p>
                <div id=\"blockEnrollmentDetails\" class=\"text-muted mb-3\"></div>
                <small class=\"text-muted\">This will prevent the student from accessing the course content.</small>
            </div>
            <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                <button type=\"button\" class=\"btn btn-secondary btn-sm\" data-bs-dismiss=\"modal\">
                    <i class=\"fas fa-times me-1\"></i>Cancel
                </button>
                <button type=\"button\" class=\"btn btn-sm\" id=\"confirmBlockBtn\" style=\"background: #ffc107; color: #212529; border: none;\">
                    <i class=\"fas fa-ban me-1\"></i>Block Enrollment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Unblock Enrollment Modal -->
<div class=\"modal fade\" id=\"unblockEnrollmentModal\" tabindex=\"-1\" aria-labelledby=\"unblockEnrollmentModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered modal-sm\">
        <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 1rem;\">
                <h6 class=\"modal-title\" id=\"unblockEnrollmentModalLabel\" style=\"font-weight: 600;\">
                    <i class=\"fas fa-check me-2\"></i>Unblock Enrollment
                </h6>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                <p class=\"mb-3\" style=\"color: #011a2d;\">Are you sure you want to unblock this enrollment?</p>
                <div id=\"unblockEnrollmentDetails\" class=\"text-muted mb-3\"></div>
                <small class=\"text-muted\">This will restore the student's access to the course content.</small>
            </div>
            <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                <button type=\"button\" class=\"btn btn-secondary btn-sm\" data-bs-dismiss=\"modal\">
                    <i class=\"fas fa-times me-1\"></i>Cancel
                </button>
                <button type=\"button\" class=\"btn btn-sm\" id=\"confirmUnblockBtn\" style=\"background: #28a745; color: white; border: none;\">
                    <i class=\"fas fa-check me-1\"></i>Unblock Enrollment
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Enrollment Modal -->
<div class=\"modal fade\" id=\"deleteEnrollmentModal\" tabindex=\"-1\" aria-labelledby=\"deleteEnrollmentModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered modal-sm\">
        <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;\">
                <h6 class=\"modal-title\" id=\"deleteEnrollmentModalLabel\" style=\"font-weight: 600;\">
                    <i class=\"fas fa-trash me-2\"></i>Delete Enrollment
                </h6>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                <p class=\"mb-3\" style=\"color: #011a2d;\">Are you sure you want to delete this enrollment?</p>
                <div id=\"deleteEnrollmentDetails\" class=\"text-muted mb-3\"></div>
                <small class=\"text-danger\"><strong>Warning:</strong> This action cannot be undone.</small>
            </div>
            <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa;\">
                <button type=\"button\" class=\"btn btn-secondary btn-sm\" data-bs-dismiss=\"modal\">
                    <i class=\"fas fa-times me-1\"></i>Cancel
                </button>
                <button type=\"button\" class=\"btn btn-sm\" id=\"confirmDeleteBtn\" style=\"background: #a90418; color: white; border: none;\">
                    <i class=\"fas fa-trash me-1\"></i>Delete Enrollment
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.enrollment-row',
        ['.enrollment-student-name', '.enrollment-course-code', '.enrollment-course-title']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Modal functions for enrollment actions
function showBlockModal(enrollmentId, studentName, courseCode) {
    document.getElementById('blockEnrollmentDetails').innerHTML = `
        <strong>\${studentName}</strong><br>
        <small>Course: \${courseCode}</small>
    `;

    const modal = new bootstrap.Modal(document.getElementById('blockEnrollmentModal'));
    const confirmBtn = document.getElementById('confirmBlockBtn');

    // Remove existing event listeners
    confirmBtn.replaceWith(confirmBtn.cloneNode(true));
    const newConfirmBtn = document.getElementById('confirmBlockBtn');

    newConfirmBtn.addEventListener('click', function() {
        modal.hide();
        blockEnrollment(enrollmentId);
    });

    modal.show();
}

function blockEnrollment(enrollmentId) {
    fetch(`{{ path('admin_enrollment_block', {'id': '0'}) }}`.replace('0', enrollmentId), {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to block enrollment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while blocking the enrollment');
    });
}

function showUnblockModal(enrollmentId, studentName, courseCode) {
    document.getElementById('unblockEnrollmentDetails').innerHTML = `
        <strong>\${studentName}</strong><br>
        <small>Course: \${courseCode}</small>
    `;

    const modal = new bootstrap.Modal(document.getElementById('unblockEnrollmentModal'));
    const confirmBtn = document.getElementById('confirmUnblockBtn');

    // Remove existing event listeners
    confirmBtn.replaceWith(confirmBtn.cloneNode(true));
    const newConfirmBtn = document.getElementById('confirmUnblockBtn');

    newConfirmBtn.addEventListener('click', function() {
        modal.hide();
        unblockEnrollment(enrollmentId);
    });

    modal.show();
}

function unblockEnrollment(enrollmentId) {
    fetch(`{{ path('admin_enrollment_unblock', {'id': '0'}) }}`.replace('0', enrollmentId), {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to unblock enrollment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while unblocking the enrollment');
    });
}

function showDeleteModal(enrollmentId, studentName, courseCode) {
    document.getElementById('deleteEnrollmentDetails').innerHTML = `
        <strong>\${studentName}</strong><br>
        <small>Course: \${courseCode}</small>
    `;

    const modal = new bootstrap.Modal(document.getElementById('deleteEnrollmentModal'));
    const confirmBtn = document.getElementById('confirmDeleteBtn');

    // Remove existing event listeners
    confirmBtn.replaceWith(confirmBtn.cloneNode(true));
    const newConfirmBtn = document.getElementById('confirmDeleteBtn');

    newConfirmBtn.addEventListener('click', function() {
        modal.hide();
        deleteEnrollment(enrollmentId);
    });

    modal.show();
}

function deleteEnrollment(enrollmentId) {
    fetch(`{{ path('admin_enrollment_delete', {'id': '0'}) }}`.replace('0', enrollmentId), {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to delete enrollment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the enrollment');
    });
}
</script>
{% endblock %}", "admin/enrollments/list.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\enrollments\\list.html.twig");
    }
}
