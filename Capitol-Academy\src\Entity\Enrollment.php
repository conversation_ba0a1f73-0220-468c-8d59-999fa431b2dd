<?php

namespace App\Entity;

use App\Repository\EnrollmentRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EnrollmentRepository::class)]
#[ORM\Table(name: 'enrollments')]
#[ORM\UniqueConstraint(name: 'unique_user_course', columns: ['user_id', 'course_id'])]
#[ORM\HasLifecycleCallbacks]
class Enrollment
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $user = null;

    #[ORM\ManyToOne(targetEntity: Course::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?Course $course = null;

    #[ORM\ManyToOne(targetEntity: Payment::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?Payment $payment = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $enrolledAt = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $completedAt = null;

    #[ORM\Column(length: 20)]
    private string $status = 'active';

    #[ORM\Column(nullable: true)]
    private ?int $progressPercentage = 0;

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $updatedAt = null;

    public function __construct()
    {
        $this->enrolledAt = new \DateTimeImmutable();
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
        $this->status = 'active';
        $this->progressPercentage = 0;
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;
        return $this;
    }

    public function getCourse(): ?Course
    {
        return $this->course;
    }

    public function setCourse(?Course $course): static
    {
        $this->course = $course;
        return $this;
    }

    public function getPayment(): ?Payment
    {
        return $this->payment;
    }

    public function setPayment(?Payment $payment): static
    {
        $this->payment = $payment;
        return $this;
    }

    public function getEnrolledAt(): ?\DateTimeImmutable
    {
        return $this->enrolledAt;
    }

    public function setEnrolledAt(\DateTimeImmutable $enrolledAt): static
    {
        $this->enrolledAt = $enrolledAt;
        return $this;
    }

    public function getCompletedAt(): ?\DateTimeImmutable
    {
        return $this->completedAt;
    }

    public function setCompletedAt(?\DateTimeImmutable $completedAt): static
    {
        $this->completedAt = $completedAt;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        // Validate status values
        if (!in_array($status, ['active', 'blocked', 'completed', 'certified'])) {
            throw new \InvalidArgumentException('Invalid status. Must be one of: active, blocked, completed, certified');
        }

        $this->status = $status;

        // Auto-set completedAt and progress when status is completed or certified
        if (in_array($status, ['completed', 'certified'])) {
            if ($this->completedAt === null) {
                $this->completedAt = new \DateTimeImmutable();
            }
            // Auto-set progress to 100% when status is completed or certified
            $this->progressPercentage = 100;
        }

        return $this;
    }

    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function isBlocked(): bool
    {
        return $this->status === 'blocked';
    }

    public function isStatusCompleted(): bool
    {
        return $this->status === 'completed';
    }

    public function isReadOnly(): bool
    {
        return $this->isCertified();
    }

    public function getProgressPercentage(): ?int
    {
        return $this->progressPercentage;
    }

    public function setProgressPercentage(?int $progressPercentage): static
    {
        $this->progressPercentage = $progressPercentage;

        // Auto-set status to completed when progress reaches 100%
        if ($progressPercentage === 100 && $this->status !== 'certified') {
            $this->status = 'completed';
            if ($this->completedAt === null) {
                $this->completedAt = new \DateTimeImmutable();
            }
        }

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeImmutable $updatedAt): static
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    public function isCompleted(): bool
    {
        return $this->completedAt !== null;
    }

    public function isCertified(): bool
    {
        return $this->status === 'certified';
    }

    public function markAsCompleted(): static
    {
        $this->status = 'completed';
        $this->completedAt = new \DateTimeImmutable();
        $this->progressPercentage = 100;
        return $this;
    }

    public function markAsCertified(): static
    {
        $this->status = 'certified';
        $this->completedAt = new \DateTimeImmutable();
        $this->progressPercentage = 100;
        return $this;
    }

    public function getStatusLabel(): string
    {
        return match($this->status) {
            'active' => 'Active',
            'blocked' => 'Blocked',
            'completed' => 'Completed',
            'certified' => 'Certified',
            default => 'Unknown'
        };
    }

    public function getStatusBadgeClass(): string
    {
        return match($this->status) {
            'active' => 'badge-success',
            'blocked' => 'badge-danger',
            'completed' => 'badge-primary',
            'certified' => 'badge-warning',
            default => 'badge-secondary'
        };
    }

    /**
     * Generate enrollment code for display
     */
    public function getEnrollmentCode(): string
    {
        $courseCode = $this->course ? $this->course->getCode() : 'ENR';
        return $courseCode . '-' . str_pad($this->id, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Generate URL-friendly identifier for enrollment details
     */
    public function getUrlIdentifier(): string
    {
        $courseCode = $this->course ? $this->course->getCode() : 'UNKNOWN';
        $studentName = $this->user ? $this->user->getFullName() : 'unknown-user';

        // Convert to lowercase and replace spaces with hyphens
        $slugifiedName = strtolower(str_replace(' ', '-', $studentName));

        return $courseCode . '-' . $slugifiedName;
    }
}
