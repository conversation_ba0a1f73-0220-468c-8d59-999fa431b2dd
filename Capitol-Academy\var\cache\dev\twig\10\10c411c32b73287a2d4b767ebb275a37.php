<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/courses/index.html.twig */
class __TwigTemplate_fdb94181d8c049609139505bbec36953 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/courses/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/courses/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Courses Management - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Courses Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item active\">Courses</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        $context["page_config"] = ["page_title" => "Courses Management", "page_icon" => "fas fa-graduation-cap", "search_placeholder" => "Search...", "create_button" => ["url" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_course_create"), "text" => "Add New Course", "icon" => "fas fa-plus"], "additional_buttons" => [["url" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses_export"), "text" => "", "icon" => "fas fa-download", "class" => "btn-outline-light", "style" => "border-radius: 50%; width: 45px; height: 45px; display: flex; align-items: center; justify-content: center;", "title" => "Export to CSV"]], "stats" => [["title" => "Total Courses", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(),         // line 35
(isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 35, $this->source); })())), "icon" => "fas fa-layer-group", "color" => "#011a2d", "gradient" => "linear-gradient(135deg, #011a2d 0%, #1a3461 100%)"], ["title" => "Active", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 42
(isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 42, $this->source); })()), function ($__course__) use ($context, $macros) { $context["course"] = $__course__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 42, $this->source); })()), "isActive", [], "any", false, false, false, 42); })), "icon" => "fas fa-check-circle", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "Inactive", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 49
(isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 49, $this->source); })()), function ($__course__) use ($context, $macros) { $context["course"] = $__course__; return  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 49, $this->source); })()), "isActive", [], "any", false, false, false, 49); })), "icon" => "fas fa-pause-circle", "color" => "#6c757d", "gradient" => "linear-gradient(135deg, #6c757d 0%, #495057 100%)"], ["title" => "Recent (30 days)", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 56
(isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 56, $this->source); })()), function ($__course__) use ($context, $macros) { $context["course"] = $__course__; return (CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 56, $this->source); })()), "createdAt", [], "any", false, false, false, 56) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 56, $this->source); })()), "createdAt", [], "any", false, false, false, 56) > $this->extensions['Twig\Extension\CoreExtension']->convertDate("-30 days"))); })), "icon" => "fas fa-clock", "color" => "#a90418", "gradient" => "linear-gradient(135deg, #a90418 0%, #8b0314 100%)"]]];
        // line 63
        yield "
<!-- Custom Layout for Courses with Statistics Below Table -->
<div class=\"card border-0 shadow-lg mb-4\">
    <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
        <div class=\"row align-items-center\">
            <div class=\"col-md-6\">
                <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                    <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                    Courses Management
                </h2>
            </div>
            <div class=\"col-md-6\">
                <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                    <!-- Professional Search -->
                    <div class=\"search-container me-3 mb-2 mb-md-0\" style=\"position: relative;\">
                        <div class=\"input-group\" style=\"width: 320px;\">
                            <input type=\"text\"
                                   id=\"professional-search\"
                                   class=\"form-control form-control-lg admin-search-input\"
                                   placeholder=\"Search...\"
                                   style=\"border: 2px solid #011a2d; background: #ffffff; color: #343a40; font-size: 1rem; transition: all 0.3s ease; box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25); border-radius: 8px 0 0 8px; outline: none;\">
                            <div class=\"input-group-append\">
                                <button type=\"button\"
                                        class=\"btn btn-lg admin-search-btn\"
                                        id=\"search-clear-btn\"
                                        style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); border: 2px solid #011a2d; border-left: none; border-radius: 0 8px 8px 0; color: white;\">
                                    <i class=\"fas fa-search\"></i>
                                </button>
                            </div>
                        </div>
                        <div id=\"search-results-count\" class=\"text-muted small mt-1\" style=\"display: none;\"></div>
                    </div>

                    <!-- Export Button -->
                    <a href=\"";
        // line 97
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_courses_export");
        yield "\"
                       class=\"btn btn-outline-light me-2 mb-2 mb-md-0\"
                       style=\"border-radius: 50%; width: 45px; height: 45px; display: flex; align-items: center; justify-content: center;\"
                       title=\"Export to CSV\">
                        <i class=\"fas fa-download\"></i>
                    </a>

                    <!-- Create Button -->
                    <a href=\"";
        // line 105
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_course_create");
        yield "\"
                       class=\"btn btn-light admin-btn-create mb-2 mb-md-0\"
                       style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem;\">
                        <i class=\"fas fa-plus me-2\"></i>
                        Add New Course
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Table Content Area -->
    <div class=\"card-body pt-0\">
        <!-- Standardized Table -->
        ";
        // line 119
        $context["table_headers"] = [["text" => "Thumbnail"], ["text" => "Code"], ["text" => "Title"], ["text" => "Category"], ["text" => "Level"], ["text" => "Duration"], ["text" => "Price"], ["text" => "Status"], ["text" => "Actions", "style" => "width: 200px;"]];
        // line 130
        yield "
        ";
        // line 131
        $context["table_rows"] = [];
        // line 132
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 132, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["course"]) {
            // line 133
            yield "            ";
            $context["row_cells"] = [["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 135
$context["course"], "thumbnailImage", [], "any", false, false, false, 135)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((((("<img src=\"" . $this->env->getFunction('course_image_url')->getCallable()(CoreExtension::getAttribute($this->env, $this->source,             // line 136
$context["course"], "thumbnailImage", [], "any", false, false, false, 136), "thumbnail")) . "\" alt=\"") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 136)) . "\" class=\"img-thumbnail shadow-sm\" style=\"width: 60px; height: 45px; object-fit: cover; border-radius: 8px; border: 2px solid #f8f9fa;\">")) : ("<div class=\"bg-light d-flex align-items-center justify-content-center shadow-sm\" style=\"width: 60px; height: 45px; border-radius: 8px; border: 2px solid #f8f9fa;\"><i class=\"fas fa-image text-muted\"></i></div>"))], ["content" => (("<code class=\"course-code bg-light text-dark\" style=\"padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 600;\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 140
$context["course"], "code", [], "any", false, false, false, 140)) . "</code>")], ["content" => (("<h6 class=\"course-title mb-0 font-weight-bold text-dark\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 143
$context["course"], "title", [], "any", false, false, false, 143)) . "</h6>")], ["content" => (("<span class=\"course-category text-dark font-weight-medium\">" . ((CoreExtension::getAttribute($this->env, $this->source,             // line 146
$context["course"], "category", [], "any", true, true, false, 146)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "category", [], "any", false, false, false, 146), "General")) : ("General"))) . "</span>")], ["content" => (("<span class=\"text-dark font-weight-medium\">" . ((CoreExtension::getAttribute($this->env, $this->source,             // line 149
$context["course"], "level", [], "any", true, true, false, 149)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "level", [], "any", false, false, false, 149), "Beginner")) : ("Beginner"))) . "</span>")], ["content" => (("<span class=\"text-dark font-weight-medium\">" . ((CoreExtension::getAttribute($this->env, $this->source,             // line 152
$context["course"], "duration", [], "any", true, true, false, 152)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "duration", [], "any", false, false, false, 152), "0")) : ("0"))) . " min</span>")], ["content" => (("<span class=\"text-dark font-weight-medium\">\$" . ((CoreExtension::getAttribute($this->env, $this->source,             // line 155
$context["course"], "price", [], "any", true, true, false, 155)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "price", [], "any", false, false, false, 155), "0")) : ("0"))) . "</span>")], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 158
$context["course"], "isActive", [], "any", false, false, false, 158)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>") : ("<span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-pause-circle mr-1\"></i> Inactive</span>"))], ["content" => (((((((((((((((((((("<div class=\"btn-group\" role=\"group\">
                        <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_course_preview", ["code" => CoreExtension::getAttribute($this->env, $this->source,             // line 164
$context["course"], "code", [], "any", false, false, false, 164)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Preview Course\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"") . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_course_edit", ["code" => CoreExtension::getAttribute($this->env, $this->source,             // line 165
$context["course"], "code", [], "any", false, false, false, 165)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Course\"><i class=\"fas fa-edit\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 166
$context["course"], "isActive", [], "any", false, false, false, 166)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#6c757d") : ("#28a745"))) . "; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "isActive", [], "any", false, false, false, 166)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Deactivate") : ("Activate"))) . " Course\" onclick=\"toggleCourseStatus(") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "id", [], "any", false, false, false, 166)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 166)) . "', ") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "isActive", [], "any", false, false, false, 166)) . ")\"><i class=\"fas fa-") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "isActive", [], "any", false, false, false, 166)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("pause") : ("play"))) . "\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Course\" onclick=\"deleteCourse(") . CoreExtension::getAttribute($this->env, $this->source,             // line 167
$context["course"], "id", [], "any", false, false, false, 167)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 167)) . "')\"><i class=\"fas fa-trash\"></i></button>
                    </div>")]];
            // line 171
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 171, $this->source); })()), [["cells" => (isset($context["row_cells"]) || array_key_exists("row_cells", $context) ? $context["row_cells"] : (function () { throw new RuntimeError('Variable "row_cells" does not exist.', 171, $this->source); })())]]);
            // line 172
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['course'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 173
        yield "
        ";
        // line 174
        yield from $this->load("components/admin_table.html.twig", 174)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 175
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 175, $this->source); })()), "rows" =>         // line 176
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 176, $this->source); })()), "row_class" => "course-row", "empty_message" => "No courses found", "empty_icon" => "fas fa-graduation-cap", "empty_description" => "Get started by adding your first course.", "search_config" => ["fields" => [".course-title", ".course-code", ".course-category"]]]));
        // line 185
        yield "
        <!-- Interactive Charts Section -->
        <div class=\"row mt-4\">
            <!-- Pie Chart: Course Distribution -->
            <div class=\"col-md-6 mb-3\">
                <div class=\"card border-0 shadow-sm\">
                    <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1rem;\">
                        <h6 class=\"mb-0\">
                            <i class=\"fas fa-chart-pie me-2\"></i>
                            Course Status Distribution
                        </h6>
                    </div>
                    <div class=\"card-body\">
                        <canvas id=\"courseStatusChart\" width=\"400\" height=\"300\"></canvas>
                    </div>
                </div>
            </div>

            <!-- Bar Chart: Course Metrics -->
            <div class=\"col-md-6 mb-3\">
                <div class=\"card border-0 shadow-sm\">
                    <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1rem;\">
                        <h6 class=\"mb-0\">
                            <i class=\"fas fa-chart-bar me-2\"></i>
                            Course Enrollments Overview
                        </h6>
                    </div>
                    <div class=\"card-body\">
                        <canvas id=\"courseEnrollmentsChart\" width=\"400\" height=\"300\"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 225
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 226
        yield "<!-- Chart.js CDN -->
<script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>

<style>
/* Chart container styling */
.card-body canvas {
    max-height: 300px;
}

/* Admin stat card styling */
.admin-stat-card {
    transition: all 0.3s ease;
    border-radius: 12px;
}

.admin-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.admin-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Search functionality styling */
.admin-search-input:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
}

.admin-search-btn:hover {
    background: linear-gradient(135deg, #1a3461 0%, #011a2d 100%) !important;
}

/* Responsive chart adjustments */
@media (max-width: 768px) {
    .card-body canvas {
        max-height: 250px;
    }
}
</style>

<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.course-row',
        ['.course-title', '.course-code', '.course-category']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize Charts
    initializeCourseCharts();
});

function initializeCourseCharts() {
    // Course Status Distribution Pie Chart
    const statusCtx = document.getElementById('courseStatusChart').getContext('2d');
    const activeCourses = ";
        // line 295
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env, (isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 295, $this->source); })()), function ($__course__) use ($context, $macros) { $context["course"] = $__course__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 295, $this->source); })()), "isActive", [], "any", false, false, false, 295); })), "html", null, true);
        yield ";
    const inactiveCourses = ";
        // line 296
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env, (isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 296, $this->source); })()), function ($__course__) use ($context, $macros) { $context["course"] = $__course__; return  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 296, $this->source); })()), "isActive", [], "any", false, false, false, 296); })), "html", null, true);
        yield ";

    new Chart(statusCtx, {
        type: 'pie',
        data: {
            labels: ['Active Courses', 'Inactive Courses'],
            datasets: [{
                data: [activeCourses, inactiveCourses],
                backgroundColor: [
                    '#28a745',  // Green for active
                    '#6c757d'   // Gray for inactive
                ],
                borderColor: [
                    '#011a2d',
                    '#011a2d'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });

    // Course Enrollments Bar Chart
    const enrollmentsCtx = document.getElementById('courseEnrollmentsChart').getContext('2d');
    const courseLabels = [
        ";
        // line 345
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(Twig\Extension\CoreExtension::slice($this->env->getCharset(), (isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 345, $this->source); })()), 0, 8));
        $context['loop'] = [
          'parent' => $context['_parent'],
          'index0' => 0,
          'index'  => 1,
          'first'  => true,
        ];
        if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
            $length = count($context['_seq']);
            $context['loop']['revindex0'] = $length - 1;
            $context['loop']['revindex'] = $length;
            $context['loop']['length'] = $length;
            $context['loop']['last'] = 1 === $length;
        }
        foreach ($context['_seq'] as $context["_key"] => $context["course"]) {
            yield "'";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "code", [], "any", false, false, false, 345), "html", null, true);
            yield "'";
            if ((($tmp =  !CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "last", [], "any", false, false, false, 345)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                yield ",";
            }
            ++$context['loop']['index0'];
            ++$context['loop']['index'];
            $context['loop']['first'] = false;
            if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                --$context['loop']['revindex0'];
                --$context['loop']['revindex'];
                $context['loop']['last'] = 0 === $context['loop']['revindex0'];
            }
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['course'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 346
        yield "    ];
    const enrollmentData = [
        ";
        // line 348
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(Twig\Extension\CoreExtension::slice($this->env->getCharset(), (isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 348, $this->source); })()), 0, 8));
        $context['loop'] = [
          'parent' => $context['_parent'],
          'index0' => 0,
          'index'  => 1,
          'first'  => true,
        ];
        if (is_array($context['_seq']) || (is_object($context['_seq']) && $context['_seq'] instanceof \Countable)) {
            $length = count($context['_seq']);
            $context['loop']['revindex0'] = $length - 1;
            $context['loop']['revindex'] = $length;
            $context['loop']['length'] = $length;
            $context['loop']['last'] = 1 === $length;
        }
        foreach ($context['_seq'] as $context["_key"] => $context["course"]) {
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["course"], "enrolledCount", [], "any", false, false, false, 348), "html", null, true);
            if ((($tmp =  !CoreExtension::getAttribute($this->env, $this->source, $context["loop"], "last", [], "any", false, false, false, 348)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                yield ",";
            }
            ++$context['loop']['index0'];
            ++$context['loop']['index'];
            $context['loop']['first'] = false;
            if (isset($context['loop']['revindex0'], $context['loop']['revindex'])) {
                --$context['loop']['revindex0'];
                --$context['loop']['revindex'];
                $context['loop']['last'] = 0 === $context['loop']['revindex0'];
            }
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['course'], $context['_parent'], $context['loop']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 349
        yield "    ];

    new Chart(enrollmentsCtx, {
        type: 'bar',
        data: {
            labels: courseLabels,
            datasets: [{
                label: 'Total Enrollments',
                data: enrollmentData,
                backgroundColor: '#011a2d',
                borderColor: '#a90418',
                borderWidth: 1,
                borderRadius: 4,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            return 'Course: ' + context[0].label;
                        },
                        label: function(context) {
                            return 'Enrollments: ' + context.parsed.y;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: 45,
                        minRotation: 0
                    }
                }
            }
        }
    });
}

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Course management functions
function toggleCourseStatus(courseId, courseTitle, currentStatus) {
    showStatusModal(courseTitle, currentStatus, function() {
        executeCourseStatusToggle(courseId);
    });
}

function deleteCourse(courseId, courseTitle) {
    showDeleteModal(courseTitle, function() {
        executeCourseDelete(courseId);
    });
}

// Actual execution functions
function executeCourseStatusToggle(courseId) {
    fetch(`/admin/courses/\${courseId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the course status');
    });
}

function executeCourseDelete(courseId) {
    fetch(`/admin/courses/\${courseId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // Check if this is an enrollment exists error
            if (data.type === 'enrollment_exists') {
                showEnrollmentExistsModal();
            } else {
                alert(data.message || 'An error occurred');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the course');
    });
}

function showEnrollmentExistsModal() {
    new bootstrap.Modal(document.getElementById('enrollmentExistsModal')).show();
}
</script>

<!-- Enrollment Exists Modal -->
<div class=\"modal fade\" id=\"enrollmentExistsModal\" tabindex=\"-1\" aria-labelledby=\"enrollmentExistsModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered modal-sm\">
        <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;\">
                <h6 class=\"modal-title\" id=\"enrollmentExistsModalLabel\" style=\"font-weight: 600;\">
                    <i class=\"fas fa-exclamation-triangle me-2\"></i>Cannot Delete Course
                </h6>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                <div class=\"mb-3\">
                    <i class=\"fas fa-users text-danger\" style=\"font-size: 3rem;\"></i>
                </div>
                <p class=\"mb-3\" style=\"color: #a90418; font-weight: 600;\">
                    Course already enrolled. Deactivate course instead.
                </p>
                <small class=\"text-muted\">This course has active enrollments and cannot be deleted. Consider deactivating it to prevent new enrollments while preserving existing data.</small>
            </div>
            <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa; justify-content: center;\">
                <button type=\"button\" class=\"btn btn-primary btn-sm\" data-bs-dismiss=\"modal\" style=\"transition: all 0.3s ease;\">
                    <i class=\"fas fa-check me-1\"></i>OK
                </button>
            </div>
        </div>
    </div>
</div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/courses/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  534 => 349,  501 => 348,  497 => 346,  462 => 345,  410 => 296,  406 => 295,  335 => 226,  322 => 225,  273 => 185,  271 => 176,  270 => 175,  269 => 174,  266 => 173,  260 => 172,  257 => 171,  254 => 167,  252 => 166,  250 => 165,  248 => 164,  246 => 158,  245 => 155,  244 => 152,  243 => 149,  242 => 146,  241 => 143,  240 => 140,  239 => 136,  238 => 135,  236 => 133,  231 => 132,  229 => 131,  226 => 130,  224 => 119,  207 => 105,  196 => 97,  160 => 63,  158 => 56,  157 => 49,  156 => 42,  155 => 35,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Courses Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Courses Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Courses</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Courses Management',
    'page_icon': 'fas fa-graduation-cap',
    'search_placeholder': 'Search...',
    'create_button': {
        'url': path('admin_course_create'),
        'text': 'Add New Course',
        'icon': 'fas fa-plus'
    },
    'additional_buttons': [
        {
            'url': path('admin_courses_export'),
            'text': '',
            'icon': 'fas fa-download',
            'class': 'btn-outline-light',
            'style': 'border-radius: 50%; width: 45px; height: 45px; display: flex; align-items: center; justify-content: center;',
            'title': 'Export to CSV'
        }
    ],
    'stats': [
        {
            'title': 'Total Courses',
            'value': courses|length,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': courses|filter(course => course.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': courses|filter(course => not course.isActive)|length,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': courses|filter(course => course.createdAt and course.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

<!-- Custom Layout for Courses with Statistics Below Table -->
<div class=\"card border-0 shadow-lg mb-4\">
    <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
        <div class=\"row align-items-center\">
            <div class=\"col-md-6\">
                <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                    <i class=\"fas fa-graduation-cap mr-3\" style=\"font-size: 2rem;\"></i>
                    Courses Management
                </h2>
            </div>
            <div class=\"col-md-6\">
                <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                    <!-- Professional Search -->
                    <div class=\"search-container me-3 mb-2 mb-md-0\" style=\"position: relative;\">
                        <div class=\"input-group\" style=\"width: 320px;\">
                            <input type=\"text\"
                                   id=\"professional-search\"
                                   class=\"form-control form-control-lg admin-search-input\"
                                   placeholder=\"Search...\"
                                   style=\"border: 2px solid #011a2d; background: #ffffff; color: #343a40; font-size: 1rem; transition: all 0.3s ease; box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25); border-radius: 8px 0 0 8px; outline: none;\">
                            <div class=\"input-group-append\">
                                <button type=\"button\"
                                        class=\"btn btn-lg admin-search-btn\"
                                        id=\"search-clear-btn\"
                                        style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); border: 2px solid #011a2d; border-left: none; border-radius: 0 8px 8px 0; color: white;\">
                                    <i class=\"fas fa-search\"></i>
                                </button>
                            </div>
                        </div>
                        <div id=\"search-results-count\" class=\"text-muted small mt-1\" style=\"display: none;\"></div>
                    </div>

                    <!-- Export Button -->
                    <a href=\"{{ path('admin_courses_export') }}\"
                       class=\"btn btn-outline-light me-2 mb-2 mb-md-0\"
                       style=\"border-radius: 50%; width: 45px; height: 45px; display: flex; align-items: center; justify-content: center;\"
                       title=\"Export to CSV\">
                        <i class=\"fas fa-download\"></i>
                    </a>

                    <!-- Create Button -->
                    <a href=\"{{ path('admin_course_create') }}\"
                       class=\"btn btn-light admin-btn-create mb-2 mb-md-0\"
                       style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem;\">
                        <i class=\"fas fa-plus me-2\"></i>
                        Add New Course
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Table Content Area -->
    <div class=\"card-body pt-0\">
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Thumbnail'},
            {'text': 'Code'},
            {'text': 'Title'},
            {'text': 'Category'},
            {'text': 'Level'},
            {'text': 'Duration'},
            {'text': 'Price'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for course in courses %}
            {% set row_cells = [
                {
                    'content': course.thumbnailImage ?
                        '<img src=\"' ~ course_image_url(course.thumbnailImage, 'thumbnail') ~ '\" alt=\"' ~ course.title ~ '\" class=\"img-thumbnail shadow-sm\" style=\"width: 60px; height: 45px; object-fit: cover; border-radius: 8px; border: 2px solid #f8f9fa;\">' :
                        '<div class=\"bg-light d-flex align-items-center justify-content-center shadow-sm\" style=\"width: 60px; height: 45px; border-radius: 8px; border: 2px solid #f8f9fa;\"><i class=\"fas fa-image text-muted\"></i></div>'
                },
                {
                    'content': '<code class=\"course-code bg-light text-dark\" style=\"padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 600;\">' ~ course.code ~ '</code>'
                },
                {
                    'content': '<h6 class=\"course-title mb-0 font-weight-bold text-dark\">' ~ course.title ~ '</h6>'
                },
                {
                    'content': '<span class=\"course-category text-dark font-weight-medium\">' ~ (course.category|default('General')) ~ '</span>'
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium\">' ~ (course.level|default('Beginner')) ~ '</span>'
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium\">' ~ (course.duration|default('0')) ~ ' min</span>'
                },
                {
                    'content': '<span class=\"text-dark font-weight-medium\">\$' ~ (course.price|default('0')) ~ '</span>'
                },
                {
                    'content': course.isActive ?
                        '<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-check-circle mr-1\"></i> Active</span>' :
                        '<span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;\"><i class=\"fas fa-pause-circle mr-1\"></i> Inactive</span>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_course_preview', {'code': course.code}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Preview Course\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"' ~ path('admin_course_edit', {'code': course.code}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Course\"><i class=\"fas fa-edit\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ' ~ (course.isActive ? '#6c757d' : '#28a745') ~ '; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (course.isActive ? 'Deactivate' : 'Activate') ~ ' Course\" onclick=\"toggleCourseStatus(' ~ course.id ~ ', \\'' ~ course.title ~ '\\', ' ~ course.isActive ~ ')\"><i class=\"fas fa-' ~ (course.isActive ? 'pause' : 'play') ~ '\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Course\" onclick=\"deleteCourse(' ~ course.id ~ ', \\'' ~ course.title ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'course-row',
            'empty_message': 'No courses found',
            'empty_icon': 'fas fa-graduation-cap',
            'empty_description': 'Get started by adding your first course.',
            'search_config': {
                'fields': ['.course-title', '.course-code', '.course-category']
            }
        } %}

        <!-- Interactive Charts Section -->
        <div class=\"row mt-4\">
            <!-- Pie Chart: Course Distribution -->
            <div class=\"col-md-6 mb-3\">
                <div class=\"card border-0 shadow-sm\">
                    <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1rem;\">
                        <h6 class=\"mb-0\">
                            <i class=\"fas fa-chart-pie me-2\"></i>
                            Course Status Distribution
                        </h6>
                    </div>
                    <div class=\"card-body\">
                        <canvas id=\"courseStatusChart\" width=\"400\" height=\"300\"></canvas>
                    </div>
                </div>
            </div>

            <!-- Bar Chart: Course Metrics -->
            <div class=\"col-md-6 mb-3\">
                <div class=\"card border-0 shadow-sm\">
                    <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1rem;\">
                        <h6 class=\"mb-0\">
                            <i class=\"fas fa-chart-bar me-2\"></i>
                            Course Enrollments Overview
                        </h6>
                    </div>
                    <div class=\"card-body\">
                        <canvas id=\"courseEnrollmentsChart\" width=\"400\" height=\"300\"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



{% endblock %}

{% block javascripts %}
<!-- Chart.js CDN -->
<script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>

<style>
/* Chart container styling */
.card-body canvas {
    max-height: 300px;
}

/* Admin stat card styling */
.admin-stat-card {
    transition: all 0.3s ease;
    border-radius: 12px;
}

.admin-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.admin-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Search functionality styling */
.admin-search-input:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
}

.admin-search-btn:hover {
    background: linear-gradient(135deg, #1a3461 0%, #011a2d 100%) !important;
}

/* Responsive chart adjustments */
@media (max-width: 768px) {
    .card-body canvas {
        max-height: 250px;
    }
}
</style>

<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.course-row',
        ['.course-title', '.course-code', '.course-category']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize Charts
    initializeCourseCharts();
});

function initializeCourseCharts() {
    // Course Status Distribution Pie Chart
    const statusCtx = document.getElementById('courseStatusChart').getContext('2d');
    const activeCourses = {{ courses|filter(course => course.isActive)|length }};
    const inactiveCourses = {{ courses|filter(course => not course.isActive)|length }};

    new Chart(statusCtx, {
        type: 'pie',
        data: {
            labels: ['Active Courses', 'Inactive Courses'],
            datasets: [{
                data: [activeCourses, inactiveCourses],
                backgroundColor: [
                    '#28a745',  // Green for active
                    '#6c757d'   // Gray for inactive
                ],
                borderColor: [
                    '#011a2d',
                    '#011a2d'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });

    // Course Enrollments Bar Chart
    const enrollmentsCtx = document.getElementById('courseEnrollmentsChart').getContext('2d');
    const courseLabels = [
        {% for course in courses|slice(0, 8) %}'{{ course.code }}'{% if not loop.last %},{% endif %}{% endfor %}
    ];
    const enrollmentData = [
        {% for course in courses|slice(0, 8) %}{{ course.enrolledCount }}{% if not loop.last %},{% endif %}{% endfor %}
    ];

    new Chart(enrollmentsCtx, {
        type: 'bar',
        data: {
            labels: courseLabels,
            datasets: [{
                label: 'Total Enrollments',
                data: enrollmentData,
                backgroundColor: '#011a2d',
                borderColor: '#a90418',
                borderWidth: 1,
                borderRadius: 4,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            return 'Course: ' + context[0].label;
                        },
                        label: function(context) {
                            return 'Enrollments: ' + context.parsed.y;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: 45,
                        minRotation: 0
                    }
                }
            }
        }
    });
}

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Course management functions
function toggleCourseStatus(courseId, courseTitle, currentStatus) {
    showStatusModal(courseTitle, currentStatus, function() {
        executeCourseStatusToggle(courseId);
    });
}

function deleteCourse(courseId, courseTitle) {
    showDeleteModal(courseTitle, function() {
        executeCourseDelete(courseId);
    });
}

// Actual execution functions
function executeCourseStatusToggle(courseId) {
    fetch(`/admin/courses/\${courseId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the course status');
    });
}

function executeCourseDelete(courseId) {
    fetch(`/admin/courses/\${courseId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // Check if this is an enrollment exists error
            if (data.type === 'enrollment_exists') {
                showEnrollmentExistsModal();
            } else {
                alert(data.message || 'An error occurred');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the course');
    });
}

function showEnrollmentExistsModal() {
    new bootstrap.Modal(document.getElementById('enrollmentExistsModal')).show();
}
</script>

<!-- Enrollment Exists Modal -->
<div class=\"modal fade\" id=\"enrollmentExistsModal\" tabindex=\"-1\" aria-labelledby=\"enrollmentExistsModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog modal-dialog-centered modal-sm\">
        <div class=\"modal-content\" style=\"border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\">
            <div class=\"modal-header\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;\">
                <h6 class=\"modal-title\" id=\"enrollmentExistsModalLabel\" style=\"font-weight: 600;\">
                    <i class=\"fas fa-exclamation-triangle me-2\"></i>Cannot Delete Course
                </h6>
                <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\" style=\"padding: 1rem; text-align: center;\">
                <div class=\"mb-3\">
                    <i class=\"fas fa-users text-danger\" style=\"font-size: 3rem;\"></i>
                </div>
                <p class=\"mb-3\" style=\"color: #a90418; font-weight: 600;\">
                    Course already enrolled. Deactivate course instead.
                </p>
                <small class=\"text-muted\">This course has active enrollments and cannot be deleted. Consider deactivating it to prevent new enrollments while preserving existing data.</small>
            </div>
            <div class=\"modal-footer\" style=\"border: none; padding: 1rem; background: #f8f9fa; justify-content: center;\">
                <button type=\"button\" class=\"btn btn-primary btn-sm\" data-bs-dismiss=\"modal\" style=\"transition: all 0.3s ease;\">
                    <i class=\"fas fa-check me-1\"></i>OK
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}", "admin/courses/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\courses\\index.html.twig");
    }
}
