{% extends 'admin/base.html.twig' %}

{% block title %}Edit Enrollment - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Enrollment{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_enrollments_list') }}">Enrollments</a></li>
<li class="breadcrumb-item active">Edit Enrollment</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-edit mr-3" style="font-size: 2rem;"></i>
                        Edit Enrollment: {{ enrollment.course.code }} - {{ enrollment.user.fullName }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Back to Enrollments Button -->
                        <a href="{{ path('admin_enrollments_list') }}"
                           class="btn mb-2 mb-md-0"
                           style="background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease; border-radius: 8px; padding: 0.75rem 1.5rem; font-weight: 600; text-decoration: none;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Enrollments
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method="post" class="needs-validation" novalidate>
            <input type="hidden" name="_token" value="{{ csrf_token('enrollment_edit') }}">
            <div class="card-body">
                <!-- Single Column Layout -->
                <div class="row">
                    <div class="col-12">
                        <!-- Status and Progress Row -->
                        <div class="row">
                            <!-- Enrollment Status -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status" class="form-label">
                                        <i class="fas fa-flag" style="color: #007bff; margin-right: 0.5rem;" aria-hidden="true"></i>
                                        Enrollment Status <span class="text-danger" aria-label="required">*</span>
                                    </label>
                                    <select class="form-select enhanced-dropdown"
                                            id="status"
                                            name="status"
                                            required
                                            aria-describedby="status_help status_error"
                                            aria-label="Select enrollment status"
                                            style="height: calc(2.25rem + 2px); font-size: 1rem; line-height: 1.5; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem; padding: 0.5rem 0.75rem;"
                                            {% if enrollment.isCertified %}disabled{% endif %}>
                                        <option value="">Choose a status...</option>
                                        <option value="active" {% if enrollment.status == 'active' %}selected{% endif %}>Active</option>
                                        <option value="blocked" {% if enrollment.status == 'blocked' %}selected{% endif %}>Blocked</option>
                                        <option value="completed" {% if enrollment.status == 'completed' %}selected{% endif %}>Completed</option>
                                        <option value="certified" {% if enrollment.status == 'certified' %}selected{% endif %}>Certified</option>
                                    </select>
                                    <div id="status_help" class="form-text text-muted" style="display: none;">
                                        Select the current status of this enrollment. {% if enrollment.isCertified %}This enrollment is certified and cannot be modified.{% else %}Use arrow keys to navigate options.{% endif %}
                                    </div>
                                    <div id="status_error" class="invalid-feedback" role="alert" aria-live="polite">
                                        Please select a valid status.
                                    </div>
                                </div>
                            </div>

                            <!-- Progress Percentage -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="progress_percentage" class="form-label">
                                        <i class="fas fa-chart-line text-primary mr-1" aria-hidden="true"></i>
                                        Progress Percentage <span class="text-danger">*</span>
                                    </label>
                                    <input type="number"
                                           class="form-control enhanced-field"
                                           id="progress_percentage"
                                           name="progress_percentage"
                                           value="{{ enrollment.progressPercentage }}"
                                           min="0"
                                           max="100"
                                           step="1"
                                           placeholder="Enter progress percentage (0-100)"
                                           aria-describedby="progress_help"
                                           required
                                           style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;"
                                           {% if enrollment.isCertified %}disabled{% endif %}>
                                    <div id="progress_help" class="form-text text-muted">
                                        Enter the student's current progress percentage (0-100)
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Mark as Certified Button Row -->
                        {% if not enrollment.isCertified %}
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-grid">
                                    <button type="button"
                                            class="btn btn-lg"
                                            onclick="showCertificationModal()"
                                            style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 1rem 2rem;">
                                        <i class="fas fa-certificate mr-2" aria-hidden="true"></i>
                                        Mark as Certified
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="alert alert-success">
                                    <i class="fas fa-certificate mr-2"></i>
                                    <strong>This enrollment is certified and cannot be modified.</strong>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Form Footer -->
            <div class="card-footer" style="background: #f8f9fa; border-top: 1px solid #dee2e6;" role="contentinfo">
                <div class="row">
                    <div class="col-md-6">
                        <button type="submit"
                                class="btn btn-lg"
                                style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;"
                                aria-describedby="submit_help"
                                {% if enrollment.isCertified %}disabled{% endif %}>
                            <i class="fas fa-save mr-2" aria-hidden="true"></i>
                            {% if enrollment.isCertified %}Certified (Read-Only){% else %}Update Enrollment{% endif %}
                        </button>
                        <div id="submit_help" class="sr-only">
                            Submit the form to update the enrollment details
                        </div>
                    </div>
                    <div class="col-md-6 text-right">
                        <a href="{{ path('admin_enrollments_list') }}"
                           class="btn btn-secondary btn-lg"
                           role="button"
                           aria-label="Cancel enrollment editing and return to enrollments list">
                            <i class="fas fa-times mr-2" aria-hidden="true"></i>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Certification Modal -->
    <div class="modal fade" id="certificationModal" tabindex="-1" aria-labelledby="certificationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                    <h5 class="modal-title" id="certificationModalLabel">
                        <i class="fas fa-certificate mr-2"></i>
                        Mark as Certified
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-3">Are you sure you want to mark this enrollment as certified?</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>Student:</strong> {{ enrollment.user.fullName }}<br>
                        <strong>Course:</strong> {{ enrollment.course.title }} ({{ enrollment.course.code }})
                    </div>
                    <p class="text-muted">This action will create a certification record for the student.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-success" onclick="processCertification()" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none; border-radius: 8px; padding: 0.75rem 1.5rem; font-weight: 600;">
                        <i class="fas fa-certificate me-2"></i>Confirm Certification
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<!-- Include Select2 for enhanced dropdowns -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
// Form validation and Select2 initialization
$(document).ready(function() {
    // Enhanced status selection with search functionality
    const statusSelect = document.getElementById('status');
    const isCertified = {{ enrollment.isCertified ? 'true' : 'false' }};

    if (statusSelect) {
        $('#status').select2({
            placeholder: 'Search and select a status...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });

        // Add change event listener for certification validation
        $('#status').on('change', function() {
            const selectedValue = $(this).val();
            if (selectedValue === 'certified' && isCertified) {
                // Show user-friendly popup
                showCertificationErrorModal();
                // Reset to previous value
                $(this).val('{{ enrollment.status }}').trigger('change');
            }
        });
    }

    // Form validation
    const form = document.querySelector('.needs-validation');
    if (form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    }
});

// Certification modal functions
function showCertificationModal() {
    const modal = new bootstrap.Modal(document.getElementById('certificationModal'));
    modal.show();
}

// Show error modal for already certified enrollment
function showCertificationErrorModal() {
    // Create modal HTML if it doesn't exist
    if (!document.getElementById('certificationErrorModal')) {
        const modalHTML = `
            <div class="modal fade" id="certificationErrorModal" tabindex="-1" aria-labelledby="certificationErrorModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white;">
                            <h5 class="modal-title" id="certificationErrorModalLabel">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Already Certified
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-certificate text-warning" style="font-size: 3rem;"></i>
                            </div>
                            <h6 class="mb-3">This enrollment is already certified.</h6>
                            <p class="text-muted mb-0">Certified enrollments cannot be changed back to other statuses.</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    const modal = new bootstrap.Modal(document.getElementById('certificationErrorModal'));
    modal.show();
}

function processCertification() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('certificationModal'));

    // Make AJAX call to certify enrollment
    fetch('{{ path('admin_enrollment_certify', {'id': enrollment.id}) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>${data.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.card'));

            // Auto-dismiss after 2 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 2000);

            // Redirect to enrollments list
            setTimeout(() => {
                window.location.href = '{{ path('admin_enrollments_list') }}';
            }, 1500);
        } else {
            alert('Error: ' + (data.error || 'Failed to certify enrollment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while processing certification');
    })
    .finally(() => {
        modal.hide();
    });
}
</script>
{% endblock %}

{% block stylesheets %}
<style>
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-group.focused .form-control,
.form-group.focused .form-select {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
}

.custom-control-input:checked ~ .custom-control-label::before {
    background-color: #28a745;
    border-color: #28a745;
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Enhanced Form Field Styling */
.enhanced-field {
    transition: all 0.3s ease;
    font-size: 1rem !important;
    border: 2px solid #ced4da !important;
}

.enhanced-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
}

.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px);
}

.enhanced-dropdown {
    transition: all 0.3s ease;
    font-size: 1rem !important;
    border: 2px solid #ced4da !important;
    background-color: #fff !important;
    cursor: pointer;
    font-weight: 500;
}

.enhanced-dropdown:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
}

.enhanced-dropdown:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px);
}

/* Select2 Custom Styling */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    padding-left: 12px !important;
    padding-right: 12px !important;
    line-height: calc(1.6em + 1.25rem) !important;
}

.select2-container--bootstrap4 .select2-selection--single:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}
</style>
{% endblock %}
