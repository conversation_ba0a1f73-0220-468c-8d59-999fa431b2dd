<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerAtWTJHw\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerAtWTJHw/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerAtWTJHw.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerAtWTJHw\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerAtWTJHw\App_KernelDevDebugContainer([
    'container.build_hash' => 'AtWTJHw',
    'container.build_id' => '35f8298a',
    'container.build_time' => 1752432019,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerAtWTJHw');
