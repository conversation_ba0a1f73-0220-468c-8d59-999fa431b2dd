{% extends 'admin/base.html.twig' %}

{% block title %}Courses Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Courses Management{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item active">Courses</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Courses Management',
    'page_icon': 'fas fa-graduation-cap',
    'search_placeholder': 'Search...',
    'create_button': {
        'url': path('admin_course_create'),
        'text': 'Add New Course',
        'icon': 'fas fa-plus'
    },
    'additional_buttons': [
        {
            'url': path('admin_courses_export'),
            'text': '',
            'icon': 'fas fa-download',
            'class': 'btn-outline-light',
            'style': 'border-radius: 50%; width: 45px; height: 45px; display: flex; align-items: center; justify-content: center;',
            'title': 'Export to CSV'
        }
    ],
    'stats': [
        {
            'title': 'Total Courses',
            'value': courses|length,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': courses|filter(course => course.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': courses|filter(course => not course.isActive)|length,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': courses|filter(course => course.createdAt and course.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Thumbnail'},
            {'text': 'Code'},
            {'text': 'Title'},
            {'text': 'Category'},
            {'text': 'Level'},
            {'text': 'Duration'},
            {'text': 'Price'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for course in courses %}
            {% set row_cells = [
                {
                    'content': course.thumbnailImage ?
                        '<img src="' ~ course_image_url(course.thumbnailImage, 'thumbnail') ~ '" alt="' ~ course.title ~ '" class="img-thumbnail shadow-sm" style="width: 60px; height: 45px; object-fit: cover; border-radius: 8px; border: 2px solid #f8f9fa;">' :
                        '<div class="bg-light d-flex align-items-center justify-content-center shadow-sm" style="width: 60px; height: 45px; border-radius: 8px; border: 2px solid #f8f9fa;"><i class="fas fa-image text-muted"></i></div>'
                },
                {
                    'content': '<code class="course-code bg-light text-dark" style="padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 600;">' ~ course.code ~ '</code>'
                },
                {
                    'content': '<h6 class="course-title mb-0 font-weight-bold text-dark">' ~ course.title ~ '</h6>'
                },
                {
                    'content': '<span class="course-category text-dark font-weight-medium">' ~ (course.category|default('General')) ~ '</span>'
                },
                {
                    'content': '<span class="text-dark font-weight-medium">' ~ (course.level|default('Beginner')) ~ '</span>'
                },
                {
                    'content': '<span class="text-dark font-weight-medium">' ~ (course.duration|default('0')) ~ ' min</span>'
                },
                {
                    'content': '<span class="text-dark font-weight-medium">$' ~ (course.price|default('0')) ~ '</span>'
                },
                {
                    'content': course.isActive ?
                        '<span class="badge" style="background: #28a745; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-check-circle mr-1"></i> Active</span>' :
                        '<span class="badge" style="background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-pause-circle mr-1"></i> Inactive</span>'
                },
                {
                    'content': '<div class="btn-group" role="group">
                        <a href="' ~ path('admin_course_preview', {'code': course.code}) ~ '" class="btn btn-sm shadow-sm" style="background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Preview Course"><i class="fas fa-eye"></i></a>
                        <a href="' ~ path('admin_course_edit', {'code': course.code}) ~ '" class="btn btn-sm shadow-sm" style="background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Edit Course"><i class="fas fa-edit"></i></a>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: ' ~ (course.isActive ? '#6c757d' : '#28a745') ~ '; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="' ~ (course.isActive ? 'Deactivate' : 'Activate') ~ ' Course" onclick="toggleCourseStatus(' ~ course.id ~ ', \'' ~ course.title ~ '\', ' ~ course.isActive ~ ')"><i class="fas fa-' ~ (course.isActive ? 'pause' : 'play') ~ '"></i></button>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;" title="Delete Course" onclick="deleteCourse(' ~ course.id ~ ', \'' ~ course.title ~ '\')"><i class="fas fa-trash"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'course-row',
            'empty_message': 'No courses found',
            'empty_icon': 'fas fa-graduation-cap',
            'empty_description': 'Get started by adding your first course.',
            'search_config': {
                'fields': ['.course-title', '.course-code', '.course-category']
            }
        } %}
    {% endblock %}
{% endembed %}

<!-- Additional Statistics Section Below Table -->
<div class="row mt-4">
    <div class="col-md-6 mb-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-users text-white" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">Total Enrollments</h6>
                        <h4 class="mb-0" style="color: #007bff;">
                            {% set total_enrollments = 0 %}
                            {% for course in courses %}
                                {% set total_enrollments = total_enrollments + course.enrolledCount %}
                            {% endfor %}
                            {{ total_enrollments }}
                        </h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-certificate text-white" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">Total Certifications</h6>
                        <h4 class="mb-0" style="color: #28a745;">
                            {% set total_certifications = 0 %}
                            {% for course in courses %}
                                {% set total_certifications = total_certifications + course.certifiedCount %}
                            {% endfor %}
                            {{ total_certifications }}
                        </h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.course-row',
        ['.course-title', '.course-code', '.course-category']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Course management functions
function toggleCourseStatus(courseId, courseTitle, currentStatus) {
    showStatusModal(courseTitle, currentStatus, function() {
        executeCourseStatusToggle(courseId);
    });
}

function deleteCourse(courseId, courseTitle) {
    showDeleteModal(courseTitle, function() {
        executeCourseDelete(courseId);
    });
}

// Actual execution functions
function executeCourseStatusToggle(courseId) {
    fetch(`/admin/courses/${courseId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the course status');
    });
}

function executeCourseDelete(courseId) {
    fetch(`/admin/courses/${courseId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // Check if this is an enrollment exists error
            if (data.type === 'enrollment_exists') {
                showEnrollmentExistsModal();
            } else {
                alert(data.message || 'An error occurred');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the course');
    });
}

function showEnrollmentExistsModal() {
    new bootstrap.Modal(document.getElementById('enrollmentExistsModal')).show();
}
</script>

<!-- Enrollment Exists Modal -->
<div class="modal fade" id="enrollmentExistsModal" tabindex="-1" aria-labelledby="enrollmentExistsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content" style="border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);">
            <div class="modal-header" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;">
                <h6 class="modal-title" id="enrollmentExistsModalLabel" style="font-weight: 600;">
                    <i class="fas fa-exclamation-triangle me-2"></i>Cannot Delete Course
                </h6>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="padding: 1rem; text-align: center;">
                <div class="mb-3">
                    <i class="fas fa-users text-danger" style="font-size: 3rem;"></i>
                </div>
                <p class="mb-3" style="color: #a90418; font-weight: 600;">
                    Course already enrolled. Deactivate course instead.
                </p>
                <small class="text-muted">This course has active enrollments and cannot be deleted. Consider deactivating it to prevent new enrollments while preserving existing data.</small>
            </div>
            <div class="modal-footer" style="border: none; padding: 1rem; background: #f8f9fa; justify-content: center;">
                <button type="button" class="btn btn-primary btn-sm" data-bs-dismiss="modal" style="transition: all 0.3s ease;">
                    <i class="fas fa-check me-1"></i>OK
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}